#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - 图形界面版
基于tkinter的可视化文件管理工具
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import threading
import os
from ssh_file_manager import SSHFileManager

class SSHGUIManager:
    def __init__(self):
        self.manager = SSHFileManager()
        self.current_server = None
        self.current_path = None

        # 排序相关变量
        self.sort_column = None
        self.sort_reverse = False

        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("项老师SSH文件管理器 v1.0")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', 
                       background='#2b2b2b', 
                       foreground='#ffffff', 
                       font=('Arial', 16, 'bold'))
        
        style.configure('Server.TButton',
                       font=('Arial', 10, 'bold'))
        
        style.configure('Treeview',
                       background='#3b3b3b',
                       foreground='#ffffff',
                       fieldbackground='#3b3b3b')
        
        style.configure('Treeview.Heading',
                       background='#4b4b4b',
                       foreground='#ffffff',
                       font=('Arial', 10, 'bold'))

        # 美化滚动条
        style.configure('Vertical.TScrollbar',
                       background='#4b4b4b',
                       troughcolor='#2b2b2b',
                       bordercolor='#4b4b4b',
                       arrowcolor='#ffffff',
                       darkcolor='#4b4b4b',
                       lightcolor='#4b4b4b')
    
    def create_widgets(self):
        """创建界面组件"""
        # 标题栏
        title_frame = tk.Frame(self.root, bg='#2b2b2b', height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = ttk.Label(title_frame, 
                               text="🎯 项老师SSH文件管理器", 
                               style='Title.TLabel')
        title_label.pack(side='left', pady=15)
        
        # 服务器选择区域
        server_frame = tk.Frame(self.root, bg='#2b2b2b', height=80)
        server_frame.pack(fill='x', padx=10, pady=5)
        server_frame.pack_propagate(False)
        
        tk.Label(server_frame, text="选择服务器:", 
                bg='#2b2b2b', fg='#ffffff', 
                font=('Arial', 12, 'bold')).pack(side='left', padx=10, pady=20)
        
        # 服务器按钮
        for server_name, config in self.manager.servers.items():
            btn = ttk.Button(server_frame, 
                           text=f"{config['name']}\n({config['host']})",
                           style='Server.TButton',
                           command=lambda s=server_name: self.connect_server(s))
            btn.pack(side='left', padx=10, pady=20)
        
        # 工具栏 - 增加高度让图标完全显示
        toolbar_frame = tk.Frame(self.root, bg='#3b3b3b', height=65)
        toolbar_frame.pack(fill='x', padx=10, pady=5)
        toolbar_frame.pack_propagate(False)

        # 工具按钮 - 增加pady让图标完全显示
        self.refresh_btn = ttk.Button(toolbar_frame, text="🔄 刷新",
                                     command=self.refresh_files)
        self.refresh_btn.pack(side='left', padx=5, pady=15)

        self.upload_btn = ttk.Button(toolbar_frame, text="📤 上传文件",
                                    command=self.upload_file)
        self.upload_btn.pack(side='left', padx=5, pady=15)

        self.download_btn = ttk.Button(toolbar_frame, text="📥 下载文件",
                                      command=self.download_file)
        self.download_btn.pack(side='left', padx=5, pady=15)

        self.delete_btn = ttk.Button(toolbar_frame, text="🗑️ 删除文件",
                                    command=self.delete_file)
        self.delete_btn.pack(side='left', padx=5, pady=15)

        self.mkdir_btn = ttk.Button(toolbar_frame, text="📁 新建目录",
                                   command=self.create_directory)
        self.mkdir_btn.pack(side='left', padx=5, pady=15)
        
        # 路径显示 - 增加高度让内容完全显示
        path_frame = tk.Frame(self.root, bg='#2b2b2b', height=55)
        path_frame.pack(fill='x', padx=10, pady=5)
        path_frame.pack_propagate(False)

        tk.Label(path_frame, text="当前路径:",
                bg='#2b2b2b', fg='#ffffff',
                font=('Arial', 10)).pack(side='left', pady=15)

        self.path_var = tk.StringVar(value="未连接")
        self.path_label = tk.Label(path_frame, textvariable=self.path_var,
                                  bg='#3b3b3b', fg='#00ff00',
                                  font=('Courier', 10), relief='sunken')
        self.path_label.pack(side='left', fill='x', expand=True, padx=10, pady=10)
        
        # 文件列表
        list_frame = tk.Frame(self.root, bg='#2b2b2b')
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 创建Treeview
        columns = ('type', 'name', 'size', 'permissions', 'mtime')
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show='headings')
        
        # 设置列标题并绑定点击事件
        self.file_tree.heading('type', text='类型', command=lambda: self.on_heading_click('type'))
        self.file_tree.heading('name', text='文件名 ↕', command=lambda: self.on_heading_click('name'))
        self.file_tree.heading('size', text='大小 ↕', command=lambda: self.on_heading_click('size'))
        self.file_tree.heading('permissions', text='权限 ↕', command=lambda: self.on_heading_click('permissions'))
        self.file_tree.heading('mtime', text='修改时间 ↕', command=lambda: self.on_heading_click('mtime'))
        
        # 设置列宽
        self.file_tree.column('type', width=60, anchor=tk.CENTER)
        self.file_tree.column('name', width=300)
        self.file_tree.column('size', width=100, anchor=tk.E)
        self.file_tree.column('permissions', width=100, anchor=tk.CENTER)
        self.file_tree.column('mtime', width=150, anchor=tk.CENTER)

        # 配置文件类型颜色标签
        self.file_tree.tag_configure('directory', foreground='#FFD700')  # 金色 - 目录
        self.file_tree.tag_configure('image', foreground='#87CEEB')      # 天蓝色 - 图片
        self.file_tree.tag_configure('document', foreground='#90EE90')   # 浅绿色 - 文档
        self.file_tree.tag_configure('code', foreground='#FFA500')       # 橙色 - 代码
        self.file_tree.tag_configure('archive', foreground='#DDA0DD')    # 紫色 - 压缩包
        self.file_tree.tag_configure('audio', foreground='#FF69B4')      # 粉色 - 音频
        self.file_tree.tag_configure('video', foreground='#FF6347')      # 番茄红 - 视频
        self.file_tree.tag_configure('default', foreground='#FFFFFF')    # 白色 - 默认
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.file_tree.yview)
        self.file_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.file_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 绑定双击事件
        self.file_tree.bind('<Double-1>', self.on_double_click)
        
        # 状态栏
        status_frame = tk.Frame(self.root, bg='#3b3b3b', height=30)
        status_frame.pack(fill='x', padx=10, pady=5)
        status_frame.pack_propagate(False)
        
        self.status_var = tk.StringVar(value="就绪")
        status_label = tk.Label(status_frame, textvariable=self.status_var,
                               bg='#3b3b3b', fg='#ffffff', 
                               font=('Arial', 10))
        status_label.pack(side='left', pady=5)
        
        # 初始状态禁用按钮
        self.set_buttons_state(False)

    def get_file_type_and_color(self, filename, is_dir):
        """根据文件名和类型返回图标和颜色标签"""
        if is_dir:
            return "📁", "directory"

        ext = os.path.splitext(filename)[1].lower()

        # 图片文件
        if ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.ico', '.webp']:
            return "🖼️", "image"
        # 文档文件
        elif ext in ['.txt', '.doc', '.docx', '.pdf', '.rtf', '.md']:
            return "📄", "document"
        # 代码文件
        elif ext in ['.py', '.js', '.html', '.css', '.php', '.java', '.cpp', '.c']:
            return "💻", "code"
        # 压缩文件
        elif ext in ['.zip', '.rar', '.7z', '.tar', '.gz']:
            return "📦", "archive"
        # 音频文件
        elif ext in ['.mp3', '.wav', '.flac', '.aac', '.ogg']:
            return "🎵", "audio"
        # 视频文件
        elif ext in ['.mp4', '.avi', '.mkv', '.mov', '.wmv']:
            return "🎬", "video"
        else:
            return "📄", "default"

    def set_buttons_state(self, enabled):
        """设置按钮状态"""
        state = 'normal' if enabled else 'disabled'
        self.refresh_btn.config(state=state)
        self.upload_btn.config(state=state)
        self.download_btn.config(state=state)
        self.delete_btn.config(state=state)
        self.mkdir_btn.config(state=state)

    def on_heading_click(self, column):
        """处理列标题点击事件"""
        # 如果点击的是同一列，则切换排序方向
        if self.sort_column == column:
            self.sort_reverse = not self.sort_reverse
        else:
            self.sort_column = column
            self.sort_reverse = False

        # 更新列标题显示
        self.update_heading_text()

        # 执行排序
        self.sort_treeview()

    def update_heading_text(self):
        """更新列标题文本，显示排序指示器"""
        headings = {
            'type': '类型',
            'name': '文件名',
            'size': '大小',
            'permissions': '权限',
            'mtime': '修改时间'
        }

        for col, text in headings.items():
            if col == self.sort_column:
                indicator = ' ↑' if not self.sort_reverse else ' ↓'
                self.file_tree.heading(col, text=text + indicator, command=lambda c=col: self.on_heading_click(c))
            else:
                self.file_tree.heading(col, text=text + ' ↕', command=lambda c=col: self.on_heading_click(c))

    def sort_treeview(self):
        """对Treeview进行排序"""
        if not self.sort_column:
            return

        # 获取所有项目
        items = [(self.file_tree.set(child, self.sort_column), child)
                for child in self.file_tree.get_children('')]

        # 根据列类型进行不同的排序
        if self.sort_column == 'size':
            # 大小排序：转换为数字
            def size_key(item):
                size_text = item[0]
                if size_text == '-':
                    return 0
                # 简单的大小排序，按字符串长度和内容
                return len(size_text) * 1000 + hash(size_text) % 1000
            items.sort(key=size_key, reverse=self.sort_reverse)
        else:
            # 文本排序
            items.sort(key=lambda x: x[0].lower(), reverse=self.sort_reverse)

        # 重新排列项目
        for index, (val, child) in enumerate(items):
            self.file_tree.move(child, '', index)

    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(message)
        self.root.update()
    
    def connect_server(self, server_name):
        """连接服务器"""
        def connect_thread():
            self.update_status(f"正在连接到 {self.manager.servers[server_name]['name']}...")
            
            ssh, sftp = self.manager.connect_server(server_name)
            if ssh and sftp:
                self.current_server = server_name
                self.current_path = self.manager.servers[server_name]['remote_path']
                self.path_var.set(self.current_path)
                
                self.set_buttons_state(True)
                self.refresh_files()
                self.update_status(f"已连接到 {self.manager.servers[server_name]['name']}")
            else:
                messagebox.showerror("连接失败", f"无法连接到 {self.manager.servers[server_name]['name']}")
                self.update_status("连接失败")
        
        threading.Thread(target=connect_thread, daemon=True).start()
    
    def refresh_files(self):
        """刷新文件列表"""
        if not self.current_server:
            return
            
        def refresh_thread():
            self.update_status("正在刷新文件列表...")
            
            # 清空现有项目
            for item in self.file_tree.get_children():
                self.file_tree.delete(item)
            
            # 获取文件列表
            files = self.manager.list_files(self.current_server, self.current_path)
            
            # 添加到树形控件
            for file_info in files:
                # 获取文件类型图标和颜色标签
                file_icon, color_tag = self.get_file_type_and_color(
                    file_info['name'],
                    file_info['type'] == 'dir'
                )
                size = self.manager.format_file_size(file_info['size']) if file_info['size'] else "-"

                # 插入项目并应用颜色标签
                self.file_tree.insert('', 'end', values=(
                    file_icon,
                    file_info['name'],
                    size,
                    file_info['permissions'],
                    file_info['mtime']
                ), tags=(color_tag,))
            
            self.update_status(f"已加载 {len(files)} 个项目")
        
        threading.Thread(target=refresh_thread, daemon=True).start()
    
    def upload_file(self):
        """上传文件"""
        if not self.current_server:
            return
            
        file_path = filedialog.askopenfilename(title="选择要上传的文件")
        if not file_path:
            return
            
        def upload_thread():
            self.update_status("正在上传文件...")
            
            result = self.manager.upload_file(self.current_server, file_path, self.current_path)
            if result:
                messagebox.showinfo("上传成功", f"文件上传成功！\n访问地址: {result}")
                self.refresh_files()
            else:
                messagebox.showerror("上传失败", "文件上传失败！")
            
            self.update_status("就绪")
        
        threading.Thread(target=upload_thread, daemon=True).start()
    
    def download_file(self):
        """下载文件"""
        selection = self.file_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要下载的文件")
            return
            
        item = self.file_tree.item(selection[0])
        file_name = item['values'][1]
        file_type = item['values'][0]
        
        if file_type == "📁":
            messagebox.showwarning("提示", "不能下载目录")
            return
            
        save_path = filedialog.asksaveasfilename(
            title="保存文件",
            initialvalue=file_name
        )
        if not save_path:
            return
            
        def download_thread():
            self.update_status("正在下载文件...")
            
            remote_file = self.current_path.rstrip('/') + '/' + file_name
            result = self.manager.download_file(self.current_server, remote_file, save_path)
            
            if result:
                messagebox.showinfo("下载成功", f"文件已保存到: {save_path}")
            else:
                messagebox.showerror("下载失败", "文件下载失败！")
            
            self.update_status("就绪")
        
        threading.Thread(target=download_thread, daemon=True).start()
    
    def delete_file(self):
        """删除文件"""
        selection = self.file_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要删除的文件")
            return
            
        item = self.file_tree.item(selection[0])
        file_name = item['values'][1]
        
        if not messagebox.askyesno("确认删除", f"确定要删除 '{file_name}' 吗？"):
            return
            
        def delete_thread():
            self.update_status("正在删除文件...")
            
            remote_file = self.current_path.rstrip('/') + '/' + file_name
            result = self.manager.delete_file(self.current_server, remote_file)
            
            if result:
                messagebox.showinfo("删除成功", f"文件 '{file_name}' 已删除")
                self.refresh_files()
            else:
                messagebox.showerror("删除失败", "文件删除失败！")
            
            self.update_status("就绪")
        
        threading.Thread(target=delete_thread, daemon=True).start()
    
    def create_directory(self):
        """创建目录"""
        dir_name = simpledialog.askstring("新建目录", "请输入目录名称:")
        if not dir_name:
            return
            
        def mkdir_thread():
            self.update_status("正在创建目录...")
            
            remote_dir = self.current_path.rstrip('/') + '/' + dir_name
            result = self.manager.create_directory(self.current_server, remote_dir)
            
            if result:
                messagebox.showinfo("创建成功", f"目录 '{dir_name}' 已创建")
                self.refresh_files()
            else:
                messagebox.showerror("创建失败", "目录创建失败！")
            
            self.update_status("就绪")
        
        threading.Thread(target=mkdir_thread, daemon=True).start()
    
    def on_double_click(self, event):
        """双击事件处理"""
        selection = self.file_tree.selection()
        if not selection:
            return
            
        item = self.file_tree.item(selection[0])
        file_name = item['values'][1]
        file_type = item['values'][0]
        
        if file_type == "📁":
            # 进入目录
            self.current_path = self.current_path.rstrip('/') + '/' + file_name
            self.path_var.set(self.current_path)
            self.refresh_files()
    
    def on_closing(self):
        """关闭程序"""
        self.manager.close_all_connections()
        self.root.destroy()
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SSHGUIManager()
    app.run()
