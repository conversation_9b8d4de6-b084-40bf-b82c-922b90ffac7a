#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师快速上传工具
拖拽文件到此脚本即可自动上传
"""

import sys
import os
from ssh_upload_tool import SSHUploader
from datetime import datetime

def quick_upload():
    """快速上传功能"""
    uploader = SSHUploader()
    
    print("🎯 项老师SSH快速上传工具")
    print("=" * 40)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        # 从命令行参数获取文件路径
        file_paths = sys.argv[1:]
    else:
        # 交互式输入
        print("请输入要上传的文件或目录路径:")
        print("(支持拖拽文件到此窗口)")
        file_path = input("路径: ").strip().strip('"')
        if not file_path:
            print("❌ 未指定文件路径")
            return
        file_paths = [file_path]
    
    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"❌ 路径不存在: {file_path}")
            continue
            
        print(f"\n📁 处理: {file_path}")
        
        # 选择服务器
        print("选择上传服务器:")
        print("1. 服务器1 (diwangzhidao.com) - 调研报告和PHP文件")
        print("2. 服务器2 (xli8.com) - 说明文档和工具")
        print("3. 两个服务器都上传")
        
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == '1':
            servers = ['server1']
        elif choice == '2':
            servers = ['server2']
        elif choice == '3':
            servers = ['server1', 'server2']
        else:
            print("❌ 无效选择，默认上传到服务器1")
            servers = ['server1']
        
        # 执行上传
        for server in servers:
            print(f"\n🚀 上传到 {server}...")
            
            if os.path.isfile(file_path):
                # 上传文件
                url = uploader.upload_file(file_path, server)
                if url:
                    print(f"✅ 文件上传成功: {url}")
                    
                    # 自动打开浏览器查看
                    try:
                        import webbrowser
                        webbrowser.open(url)
                        print("🌐 已在浏览器中打开")
                    except:
                        pass
                        
            elif os.path.isdir(file_path):
                # 上传目录
                url = uploader.upload_directory(file_path, server)
                if url:
                    print(f"✅ 目录上传成功: {url}")
                    
                    # 自动打开浏览器查看
                    try:
                        import webbrowser
                        webbrowser.open(url)
                        print("🌐 已在浏览器中打开")
                    except:
                        pass
    
    print("\n🎉 上传任务完成！")
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        quick_upload()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        input("按回车键退出...")
