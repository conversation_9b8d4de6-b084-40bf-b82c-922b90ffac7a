#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH+SFTP文件管理工具
支持文件查看、上传、下载、删除等全部功能
"""

import paramiko
import os
import sys
import time
import stat
from datetime import datetime
from pathlib import Path

class SSHFileManager:
    def __init__(self):
        # 服务器配置信息
        self.servers = {
            'server1': {
                'name': '调研报告服务器',
                'host': '************',
                'port': 22,
                'username': 'root',
                'password': 'Zxczxczxc111.',
                'remote_path': '/www/wwwroot/www.diwangzhidao.com/MCP/xiangliang/1/',
                'url_base': 'https://www.diwangzhidao.com/MCP/xiangliang/1/'
            },
            'server2': {
                'name': '说明文档服务器',
                'host': '*************',
                'port': 22,
                'username': 'root',
                'password': 'Zxczxczxc111.',
                'remote_path': '/www/wwwroot/www.xli8.com/shuomingwendang/',
                'url_base': 'http://www.xli8.com/shuomingwendang/'
            }
        }
        self.current_connections = {}  # 保存活跃连接
    
    def test_connection(self, server_config):
        """测试SSH连接"""
        try:
            print(f"🔍 正在测试连接到 {server_config['host']}...")
            
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            ssh.connect(
                hostname=server_config['host'],
                port=server_config['port'],
                username=server_config['username'],
                password=server_config['password'],
                timeout=10
            )
            
            # 测试执行命令
            stdin, stdout, stderr = ssh.exec_command('pwd')
            result = stdout.read().decode().strip()
            
            ssh.close()
            print(f"✅ SSH连接成功！当前目录: {result}")
            return True
            
        except Exception as e:
            print(f"❌ SSH连接失败: {str(e)}")
            return False
    
    def upload_file(self, local_file, server_name='server1', remote_filename=None):
        """上传文件到指定服务器"""
        if server_name not in self.servers:
            print(f"❌ 服务器配置不存在: {server_name}")
            return None
            
        server_config = self.servers[server_name]
        
        if not os.path.exists(local_file):
            print(f"❌ 本地文件不存在: {local_file}")
            return None
        
        try:
            print(f"🚀 开始上传文件到 {server_config['host']}...")
            
            # 建立SSH连接
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            ssh.connect(
                hostname=server_config['host'],
                port=server_config['port'],
                username=server_config['username'],
                password=server_config['password'],
                timeout=30
            )
            
            # 建立SFTP连接
            sftp = ssh.open_sftp()
            
            # 确定远程文件名
            if remote_filename is None:
                remote_filename = os.path.basename(local_file)
            
            remote_file_path = server_config['remote_path'] + remote_filename
            
            # 确保远程目录存在
            try:
                sftp.stat(server_config['remote_path'])
            except FileNotFoundError:
                print(f"📁 创建远程目录: {server_config['remote_path']}")
                ssh.exec_command(f"mkdir -p {server_config['remote_path']}")
            
            # 上传文件
            print(f"📤 正在上传: {local_file} -> {remote_file_path}")
            sftp.put(local_file, remote_file_path)
            
            # 设置文件权限
            sftp.chmod(remote_file_path, 0o644)
            
            sftp.close()
            ssh.close()
            
            # 生成访问URL
            access_url = server_config['url_base'] + remote_filename
            
            print(f"✅ 文件上传成功！")
            print(f"🔗 访问地址: {access_url}")
            
            return access_url
            
        except Exception as e:
            print(f"❌ 上传失败: {str(e)}")
            return None
    
    def upload_directory(self, local_dir, server_name='server1', remote_dir_name=None):
        """上传整个目录"""
        if not os.path.exists(local_dir):
            print(f"❌ 本地目录不存在: {local_dir}")
            return None
            
        server_config = self.servers[server_name]
        
        if remote_dir_name is None:
            remote_dir_name = os.path.basename(local_dir)
        
        try:
            print(f"🚀 开始上传目录到 {server_config['host']}...")
            
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            ssh.connect(
                hostname=server_config['host'],
                port=server_config['port'],
                username=server_config['username'],
                password=server_config['password'],
                timeout=30
            )
            
            sftp = ssh.open_sftp()
            
            remote_base_path = server_config['remote_path'] + remote_dir_name + '/'
            
            # 创建远程目录
            ssh.exec_command(f"mkdir -p {remote_base_path}")
            
            uploaded_files = []
            
            # 递归上传文件
            for root, dirs, files in os.walk(local_dir):
                for file in files:
                    local_file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(local_file_path, local_dir)
                    remote_file_path = remote_base_path + relative_path.replace('\\', '/')
                    
                    # 创建远程子目录
                    remote_dir = os.path.dirname(remote_file_path)
                    ssh.exec_command(f"mkdir -p {remote_dir}")
                    
                    print(f"📤 上传: {relative_path}")
                    sftp.put(local_file_path, remote_file_path)
                    sftp.chmod(remote_file_path, 0o644)
                    
                    uploaded_files.append(relative_path)
            
            sftp.close()
            ssh.close()
            
            access_url = server_config['url_base'] + remote_dir_name + '/'
            
            print(f"✅ 目录上传成功！共上传 {len(uploaded_files)} 个文件")
            print(f"🔗 访问地址: {access_url}")
            
            return access_url
            
        except Exception as e:
            print(f"❌ 目录上传失败: {str(e)}")
            return None

def main():
    """主函数 - 演示用法"""
    uploader = SSHUploader()
    
    print("=" * 50)
    print("🎯 项老师SSH全自动上传工具")
    print("=" * 50)
    
    # 测试两个服务器连接
    print("\n📡 测试服务器连接...")
    for server_name, config in uploader.servers.items():
        print(f"\n测试 {server_name} ({config['host']}):")
        uploader.test_connection(config)
    
    print("\n🎉 SSH上传工具初始化完成！")
    print("\n使用方法:")
    print("uploader.upload_file('本地文件路径', 'server1')  # 上传到服务器1")
    print("uploader.upload_file('本地文件路径', 'server2')  # 上传到服务器2")
    print("uploader.upload_directory('本地目录', 'server1')  # 上传整个目录")

if __name__ == "__main__":
    main()
