#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - FTP风格完整版
类似FTP客户端的完整文件管理功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import threading
import os
import stat
from datetime import datetime
from ssh_file_manager import SSHFileManager

class SSHFTPManager:
    def __init__(self):
        self.manager = SSHFileManager()
        self.current_server = None
        self.current_remote_path = None
        self.current_local_path = os.getcwd()
        
        # 创建酷炫的主窗口
        self.root = tk.Tk()
        self.root.title("项老师SSH文件管理器 - 酷炫版 v1.0")
        self.root.geometry("1400x900")
        self.root.configure(bg='#1f293a')
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动呼吸灯动画
        self.breathing_animation()
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', 
                       background='#2b2b2b', 
                       foreground='#ffffff', 
                       font=('Microsoft YaHei', 16, 'bold'))
        
        style.configure('Path.TLabel',
                       background='#3b3b3b',
                       foreground='#00ff00',
                       font=('Courier', 10))
        
        style.configure('Server.TButton',
                       font=('Microsoft YaHei', 10, 'bold'))
        
        # 新的酷炫色系配置
        style.configure('Treeview',
                       background='#1f293a',
                       foreground='#ffffff',
                       fieldbackground='#1f293a',
                       borderwidth=0,
                       relief='flat')

        style.configure('Treeview.Heading',
                       background='#2c4766',
                       foreground='#0ef',
                       font=('Microsoft YaHei', 10, 'bold'),
                       borderwidth=1,
                       relief='flat')

        # 酷炫的文件类型颜色配置
        self.root.tk.call('ttk::style', 'configure', 'folder.Treeview', '-foreground', '#0ef')      # 青色文件夹
        self.root.tk.call('ttk::style', 'configure', 'image.Treeview', '-foreground', '#ff6b9d')    # 粉色图片
        self.root.tk.call('ttk::style', 'configure', 'code.Treeview', '-foreground', '#4ecdc4')     # 青绿色代码
        self.root.tk.call('ttk::style', 'configure', 'document.Treeview', '-foreground', '#a8e6cf') # 浅绿色文档
        self.root.tk.call('ttk::style', 'configure', 'archive.Treeview', '-foreground', '#c7ceea')  # 浅紫色压缩包
        self.root.tk.call('ttk::style', 'configure', 'audio.Treeview', '-foreground', '#ffd93d')    # 金黄色音频
        self.root.tk.call('ttk::style', 'configure', 'video.Treeview', '-foreground', '#ff6b6b')    # 红色视频
        self.root.tk.call('ttk::style', 'configure', 'executable.Treeview', '-foreground', '#ff8c42') # 橙色可执行文件
        self.root.tk.call('ttk::style', 'configure', 'default.Treeview', '-foreground', '#ffffff')  # 白色默认

        # 按钮样式配置
        style.configure('Cyber.TButton',
                       background='#2c4766',
                       foreground='#0ef',
                       borderwidth=2,
                       focuscolor='none',
                       font=('Microsoft YaHei', 9, 'bold'))

        style.map('Cyber.TButton',
                 background=[('active', '#0ef'),
                           ('pressed', '#2c4766')],
                 foreground=[('active', '#1f293a'),
                           ('pressed', '#0ef')])

        # 输入框样式
        style.configure('Cyber.TEntry',
                       fieldbackground='#212121',
                       foreground='#ffffff',
                       borderwidth=2,
                       insertcolor='#0ef')

        style.map('Cyber.TEntry',
                 focuscolor=[('focus', '#0ef')],
                 bordercolor=[('focus', '#0ef')])
    
    def create_widgets(self):
        """创建界面组件"""
        # 直接创建服务器连接区域，去掉标题栏
        self.create_server_panel()
        
        # 酷炫的主要内容区域 - 分为左右两栏
        main_frame = tk.Frame(self.root, bg='#1f293a')
        main_frame.pack(fill='both', expand=True, padx=5, pady=3)
        
        # 左侧 - 本地文件
        self.create_local_panel(main_frame)
        
        # 中间分隔线
        separator = ttk.Separator(main_frame, orient='vertical')
        separator.pack(side='left', fill='y', padx=5)
        
        # 右侧 - 远程文件
        self.create_remote_panel(main_frame)
        
        # 底部状态栏
        self.create_status_bar()
    
    def create_server_panel(self):
        """创建酷炫的服务器连接面板"""
        server_frame = tk.Frame(self.root, bg='#1f293a', height=70)
        server_frame.pack(fill='x', padx=5, pady=5)
        server_frame.pack_propagate(False)

        # 创建发光效果的标签
        tk.Label(server_frame, text="🌐 服务器连接",
                bg='#1f293a', fg='#0ef',
                font=('Microsoft YaHei', 12, 'bold')).pack(side='left', padx=15, pady=20)

        # 酷炫的服务器按钮
        for server_name, config in self.manager.servers.items():
            btn = ttk.Button(server_frame,
                           text=f"🖥️ {config['name']}\n📡 {config['host']}",
                           style='Cyber.TButton',
                           command=lambda s=server_name: self.connect_server(s))
            btn.pack(side='left', padx=8, pady=15)

        # 酷炫的断开连接按钮
        self.disconnect_btn = ttk.Button(server_frame, text="🔌 断开连接",
                                        style='Cyber.TButton',
                                        command=self.disconnect_server,
                                        state='disabled')
        self.disconnect_btn.pack(side='right', padx=15, pady=15)
    
    def create_local_panel(self, parent):
        """创建酷炫的本地文件面板"""
        local_frame = tk.Frame(parent, bg='#1f293a')
        local_frame.pack(side='left', fill='both', expand=True)
        
        # 酷炫的本地文件标题
        local_header = tk.Frame(local_frame, bg='#2c4766', height=35)
        local_header.pack(fill='x', pady=(0, 3))
        local_header.pack_propagate(False)

        tk.Label(local_header, text="💻 本地文件",
                bg='#2c4766', fg='#0ef',
                font=('Microsoft YaHei', 11, 'bold')).pack(side='left', padx=12, pady=8)
        
        # 酷炫的本地路径导航
        local_path_frame = tk.Frame(local_frame, bg='#1f293a', height=32)
        local_path_frame.pack(fill='x', pady=(0, 3))
        local_path_frame.pack_propagate(False)

        tk.Label(local_path_frame, text="📍",
                bg='#1f293a', fg='#0ef',
                font=('Microsoft YaHei', 10)).pack(side='left', padx=8, pady=6)

        self.local_path_var = tk.StringVar(value=self.current_local_path)
        self.local_path_entry = ttk.Entry(local_path_frame, textvariable=self.local_path_var,
                                         style='Cyber.TEntry',
                                         font=('Courier', 9))
        self.local_path_entry.pack(side='left', fill='x', expand=True, padx=3, pady=4)
        self.local_path_entry.bind('<Return>', self.change_local_path)

        ttk.Button(local_path_frame, text="🎯",
                  style='Cyber.TButton',
                  command=self.change_local_path).pack(side='right', padx=2, pady=4)
        ttk.Button(local_path_frame, text="🖥️",
                  style='Cyber.TButton',
                  command=self.go_to_desktop).pack(side='right', padx=2, pady=4)
        
        # 酷炫的本地文件操作按钮
        local_btn_frame = tk.Frame(local_frame, bg='#1f293a', height=35)
        local_btn_frame.pack(fill='x', pady=(0, 3))
        local_btn_frame.pack_propagate(False)

        ttk.Button(local_btn_frame, text="🔄",
                  style='Cyber.TButton',
                  command=self.refresh_local).pack(side='left', padx=3, pady=5)
        ttk.Button(local_btn_frame, text="📁+",
                  style='Cyber.TButton',
                  command=self.create_local_folder).pack(side='left', padx=3, pady=5)
        ttk.Button(local_btn_frame, text="📤",
                  style='Cyber.TButton',
                  command=self.upload_selected).pack(side='left', padx=3, pady=5)
        
        # 酷炫的本地文件列表
        local_list_frame = tk.Frame(local_frame, bg='#1f293a')
        local_list_frame.pack(fill='both', expand=True)
        
        # 创建本地文件树
        local_columns = ('name', 'size', 'type', 'modified')
        self.local_tree = ttk.Treeview(local_list_frame, columns=local_columns, show='headings')
        
        # 设置列标题
        self.local_tree.heading('name', text='文件名')
        self.local_tree.heading('size', text='大小')
        self.local_tree.heading('type', text='类型')
        self.local_tree.heading('modified', text='修改时间')
        
        # 设置列宽
        self.local_tree.column('name', width=200)
        self.local_tree.column('size', width=80, anchor=tk.E)
        self.local_tree.column('type', width=60, anchor=tk.CENTER)
        self.local_tree.column('modified', width=120, anchor=tk.CENTER)
        
        # 添加滚动条
        local_scrollbar = ttk.Scrollbar(local_list_frame, orient='vertical', command=self.local_tree.yview)
        self.local_tree.configure(yscrollcommand=local_scrollbar.set)
        
        # 布局
        self.local_tree.pack(side='left', fill='both', expand=True)
        local_scrollbar.pack(side='right', fill='y')
        
        # 绑定双击事件和右键菜单
        self.local_tree.bind('<Double-1>', self.on_local_double_click)
        self.local_tree.bind('<Button-3>', self.show_local_context_menu)

        # 绑定列标题点击排序
        for col in local_columns:
            self.local_tree.heading(col, command=lambda c=col: self.sort_local_column(c))
        
        # 初始化本地文件列表
        self.refresh_local()
    
    def create_remote_panel(self, parent):
        """创建酷炫的远程文件面板"""
        remote_frame = tk.Frame(parent, bg='#1f293a')
        remote_frame.pack(side='right', fill='both', expand=True)
        
        # 酷炫的远程文件标题
        remote_header = tk.Frame(remote_frame, bg='#2c4766', height=35)
        remote_header.pack(fill='x', pady=(0, 3))
        remote_header.pack_propagate(False)

        tk.Label(remote_header, text="🌐 远程文件",
                bg='#2c4766', fg='#0ef',
                font=('Microsoft YaHei', 11, 'bold')).pack(side='left', padx=12, pady=8)
        
        # 酷炫的远程路径导航
        remote_path_frame = tk.Frame(remote_frame, bg='#1f293a', height=32)
        remote_path_frame.pack(fill='x', pady=(0, 3))
        remote_path_frame.pack_propagate(False)

        tk.Label(remote_path_frame, text="🌍",
                bg='#1f293a', fg='#0ef',
                font=('Microsoft YaHei', 10)).pack(side='left', padx=8, pady=6)

        self.remote_path_var = tk.StringVar(value="未连接")
        self.remote_path_entry = ttk.Entry(remote_path_frame, textvariable=self.remote_path_var,
                                          style='Cyber.TEntry',
                                          font=('Courier', 9), state='readonly')
        self.remote_path_entry.pack(side='left', fill='x', expand=True, padx=3, pady=4)

        ttk.Button(remote_path_frame, text="🏠",
                  style='Cyber.TButton',
                  command=self.go_remote_home).pack(side='right', padx=2, pady=4)
        ttk.Button(remote_path_frame, text="⬆️",
                  style='Cyber.TButton',
                  command=self.go_remote_parent).pack(side='right', padx=2, pady=4)
        
        # 酷炫的远程文件操作按钮
        remote_btn_frame = tk.Frame(remote_frame, bg='#1f293a', height=35)
        remote_btn_frame.pack(fill='x', pady=(0, 3))
        remote_btn_frame.pack_propagate(False)

        ttk.Button(remote_btn_frame, text="🔄",
                  style='Cyber.TButton',
                  command=self.refresh_remote).pack(side='left', padx=3, pady=5)
        ttk.Button(remote_btn_frame, text="📁+",
                  style='Cyber.TButton',
                  command=self.create_remote_folder).pack(side='left', padx=3, pady=5)
        ttk.Button(remote_btn_frame, text="📥",
                  style='Cyber.TButton',
                  command=self.download_selected).pack(side='left', padx=3, pady=5)
        ttk.Button(remote_btn_frame, text="🗑️",
                  style='Cyber.TButton',
                  command=self.delete_selected).pack(side='left', padx=3, pady=5)
        
        # 酷炫的远程文件列表
        remote_list_frame = tk.Frame(remote_frame, bg='#1f293a')
        remote_list_frame.pack(fill='both', expand=True)
        
        # 创建远程文件树
        remote_columns = ('name', 'size', 'permissions', 'modified')
        self.remote_tree = ttk.Treeview(remote_list_frame, columns=remote_columns, show='headings')
        
        # 设置列标题
        self.remote_tree.heading('name', text='文件名')
        self.remote_tree.heading('size', text='大小')
        self.remote_tree.heading('permissions', text='权限')
        self.remote_tree.heading('modified', text='修改时间')
        
        # 设置列宽
        self.remote_tree.column('name', width=200)
        self.remote_tree.column('size', width=80, anchor=tk.E)
        self.remote_tree.column('permissions', width=80, anchor=tk.CENTER)
        self.remote_tree.column('modified', width=120, anchor=tk.CENTER)
        
        # 添加滚动条
        remote_scrollbar = ttk.Scrollbar(remote_list_frame, orient='vertical', command=self.remote_tree.yview)
        self.remote_tree.configure(yscrollcommand=remote_scrollbar.set)
        
        # 布局
        self.remote_tree.pack(side='left', fill='both', expand=True)
        remote_scrollbar.pack(side='right', fill='y')
        
        # 绑定双击事件和右键菜单
        self.remote_tree.bind('<Double-1>', self.on_remote_double_click)
        self.remote_tree.bind('<Button-3>', self.show_remote_context_menu)

        # 绑定列标题点击排序
        for col in remote_columns:
            self.remote_tree.heading(col, command=lambda c=col: self.sort_remote_column(c))
    
    def create_status_bar(self):
        """创建酷炫的状态栏"""
        status_frame = tk.Frame(self.root, bg='#2c4766', height=28)
        status_frame.pack(fill='x', padx=5, pady=3)
        status_frame.pack_propagate(False)

        self.status_var = tk.StringVar(value="⚡ 就绪 - 请连接服务器")
        status_label = tk.Label(status_frame, textvariable=self.status_var,
                               bg='#2c4766', fg='#ffffff',
                               font=('Microsoft YaHei', 9))
        status_label.pack(side='left', pady=6, padx=10)

        # 酷炫的连接状态指示
        self.connection_var = tk.StringVar(value="🔴 未连接")
        connection_label = tk.Label(status_frame, textvariable=self.connection_var,
                                   bg='#2c4766', fg='#0ef',
                                   font=('Microsoft YaHei', 9, 'bold'))
        connection_label.pack(side='right', pady=6, padx=10)
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(message)
        self.root.update()

    def extract_filename(self, filename_with_icon):
        """从带图标的文件名中提取真实文件名"""
        if not filename_with_icon:
            return filename_with_icon
        # 移除图标，获取真实文件名
        if ' ' in filename_with_icon and len(filename_with_icon.split(' ', 1)) > 1:
            return filename_with_icon.split(' ', 1)[1]
        return filename_with_icon
    
    def format_file_size(self, size):
        """格式化文件大小"""
        if size is None:
            return "-"
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f}{unit}"
            size /= 1024.0
        return f"{size:.1f}TB"

    def get_file_icon(self, filename, is_dir=False):
        """根据文件类型获取图标"""
        if is_dir:
            return "📁"

        # 获取文件扩展名
        ext = os.path.splitext(filename)[1].lower()

        # 图标映射
        icon_map = {
            # 图片文件
            '.jpg': '🖼️', '.jpeg': '🖼️', '.png': '🖼️', '.gif': '🖼️',
            '.bmp': '🖼️', '.svg': '🖼️', '.ico': '🖼️', '.webp': '🖼️',

            # 文档文件
            '.txt': '📄', '.doc': '📝', '.docx': '📝', '.pdf': '📕',
            '.rtf': '📝', '.odt': '📝',

            # 表格文件
            '.xls': '📊', '.xlsx': '📊', '.csv': '📊', '.ods': '📊',

            # 演示文件
            '.ppt': '📊', '.pptx': '📊', '.odp': '📊',

            # 代码文件
            '.html': '🌐', '.htm': '🌐', '.css': '🎨', '.js': '⚡',
            '.php': '🐘', '.py': '🐍', '.java': '☕', '.cpp': '⚙️',
            '.c': '⚙️', '.h': '⚙️', '.cs': '🔷', '.go': '🐹',
            '.rs': '🦀', '.swift': '🦉', '.kt': '🎯',

            # 压缩文件
            '.zip': '📦', '.rar': '📦', '.7z': '📦', '.tar': '📦',
            '.gz': '📦', '.bz2': '📦', '.xz': '📦',

            # 音频文件
            '.mp3': '🎵', '.wav': '🎵', '.flac': '🎵', '.aac': '🎵',
            '.ogg': '🎵', '.wma': '🎵', '.m4a': '🎵',

            # 视频文件
            '.mp4': '🎬', '.avi': '🎬', '.mkv': '🎬', '.mov': '🎬',
            '.wmv': '🎬', '.flv': '🎬', '.webm': '🎬',

            # 数据库文件
            '.sql': '🗄️', '.db': '🗄️', '.sqlite': '🗄️', '.mdb': '🗄️',

            # 配置文件
            '.json': '⚙️', '.xml': '⚙️', '.yml': '⚙️', '.yaml': '⚙️',
            '.ini': '⚙️', '.conf': '⚙️', '.cfg': '⚙️',

            # 可执行文件
            '.exe': '⚡', '.msi': '📦', '.deb': '📦', '.rpm': '📦',
            '.dmg': '💿', '.app': '📱',

            # 字体文件
            '.ttf': '🔤', '.otf': '🔤', '.woff': '🔤', '.woff2': '🔤',
        }

        return icon_map.get(ext, '📄')

    def get_file_color(self, filename, is_dir=False):
        """根据文件类型获取颜色标签"""
        if is_dir:
            return 'folder'

        ext = os.path.splitext(filename)[1].lower()

        # 颜色分类
        if ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.ico', '.webp']:
            return 'image'
        elif ext in ['.html', '.htm', '.css', '.js', '.php', '.py', '.java', '.cpp', '.c', '.h']:
            return 'code'
        elif ext in ['.txt', '.doc', '.docx', '.pdf', '.rtf']:
            return 'document'
        elif ext in ['.zip', '.rar', '.7z', '.tar', '.gz']:
            return 'archive'
        elif ext in ['.mp3', '.wav', '.flac', '.aac', '.ogg']:
            return 'audio'
        elif ext in ['.mp4', '.avi', '.mkv', '.mov', '.wmv']:
            return 'video'
        elif ext in ['.exe', '.msi', '.deb', '.rpm']:
            return 'executable'
        else:
            return 'default'
    
    def connect_server(self, server_name):
        """连接服务器"""
        def connect_thread():
            self.update_status(f"正在连接到 {self.manager.servers[server_name]['name']}...")
            
            ssh, sftp = self.manager.connect_server(server_name)
            if ssh and sftp:
                self.current_server = server_name
                self.current_remote_path = self.manager.servers[server_name]['remote_path']
                self.remote_path_var.set(self.current_remote_path)
                
                self.connection_var.set(f"🟢 已连接: {self.manager.servers[server_name]['name']}")
                self.disconnect_btn.config(state='normal')
                
                self.refresh_remote()
                self.update_status(f"已连接到 {self.manager.servers[server_name]['name']}")
            else:
                messagebox.showerror("连接失败", f"无法连接到 {self.manager.servers[server_name]['name']}")
                self.update_status("连接失败")
        
        threading.Thread(target=connect_thread, daemon=True).start()
    
    def disconnect_server(self):
        """断开服务器连接"""
        if self.current_server:
            self.manager.disconnect_server(self.current_server)
            self.current_server = None
            self.current_remote_path = None
            self.remote_path_var.set("未连接")
            self.connection_var.set("🔴 未连接")
            self.disconnect_btn.config(state='disabled')

            # 清空远程文件列表
            for item in self.remote_tree.get_children():
                self.remote_tree.delete(item)

            self.update_status("已断开连接")

    def go_to_desktop(self):
        """跳转到桌面"""
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        if os.path.exists(desktop_path):
            self.current_local_path = desktop_path
            self.local_path_var.set(self.current_local_path)
            self.refresh_local()
            self.update_status("已跳转到桌面")
        else:
            # 尝试中文桌面路径
            desktop_path = os.path.join(os.path.expanduser("~"), "桌面")
            if os.path.exists(desktop_path):
                self.current_local_path = desktop_path
                self.local_path_var.set(self.current_local_path)
                self.refresh_local()
                self.update_status("已跳转到桌面")
            else:
                messagebox.showwarning("提示", "无法找到桌面路径")

    def sort_local_column(self, col):
        """本地文件列排序"""
        # 获取当前数据
        data = []
        for child in self.local_tree.get_children():
            values = self.local_tree.item(child)['values']
            if values[0] not in ['...', '..']:  # 排除特殊项
                data.append((child, values))

        # 根据列进行排序
        if col == 'name':
            data.sort(key=lambda x: x[1][0].lower())
        elif col == 'size':
            data.sort(key=lambda x: self._parse_size(x[1][1]), reverse=True)
        elif col == 'modified':
            data.sort(key=lambda x: x[1][3], reverse=True)
        elif col == 'type':
            data.sort(key=lambda x: (x[1][2] != '📁', x[1][0].lower()))  # 文件夹优先

        # 重新排列
        for index, (child, values) in enumerate(data):
            self.local_tree.move(child, '', index + 1)  # +1 因为第一个是"..."

    def sort_remote_column(self, col):
        """远程文件列排序"""
        # 获取当前数据
        data = []
        for child in self.remote_tree.get_children():
            values = self.remote_tree.item(child)['values']
            if values[0] not in ['...', '..']:  # 排除特殊项
                data.append((child, values))

        # 根据列进行排序
        if col == 'name':
            data.sort(key=lambda x: x[1][0].lower())
        elif col == 'size':
            data.sort(key=lambda x: self._parse_size(x[1][1]), reverse=True)
        elif col == 'modified':
            data.sort(key=lambda x: x[1][3], reverse=True)
        elif col == 'permissions':
            data.sort(key=lambda x: (x[1][2].startswith('d'), x[1][0].lower()), reverse=True)  # 目录优先

        # 重新排列
        for index, (child, values) in enumerate(data):
            self.remote_tree.move(child, '', index + 1)  # +1 因为第一个是"..."

    def _parse_size(self, size_str):
        """解析文件大小字符串为数字"""
        if not size_str or size_str == '-':
            return 0

        size_str = size_str.strip()
        if size_str.endswith('B'):
            return float(size_str[:-1])
        elif size_str.endswith('KB'):
            return float(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return float(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return float(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            try:
                return float(size_str)
            except:
                return 0
    
    def refresh_local(self):
        """刷新本地文件列表"""
        # 清空现有项目
        for item in self.local_tree.get_children():
            self.local_tree.delete(item)
        
        try:
            # 添加上级目录项 - 总是显示在最顶部
            self.local_tree.insert('', 'end', values=('...', '', '📁', '上级目录'))
            
            # 获取目录内容
            items = []
            for item in os.listdir(self.current_local_path):
                item_path = os.path.join(self.current_local_path, item)
                try:
                    stat_info = os.stat(item_path)
                    size = stat_info.st_size if os.path.isfile(item_path) else None
                    size_str = self.format_file_size(size) if size is not None else ""
                    file_type = "📁" if os.path.isdir(item_path) else "📄"
                    modified = datetime.fromtimestamp(stat_info.st_mtime).strftime('%Y-%m-%d %H:%M')
                    
                    items.append((item, size_str, file_type, modified, os.path.isdir(item_path)))
                except:
                    continue
            
            # 排序：目录在前，文件在后
            items.sort(key=lambda x: (not x[4], x[0].lower()))
            
            # 添加到树形控件
            for item, size_str, file_type, modified, is_dir in items:
                # 获取文件图标和颜色
                icon = self.get_file_icon(item, is_dir)
                color_tag = self.get_file_color(item, is_dir)

                # 插入项目并设置颜色标签
                item_id = self.local_tree.insert('', 'end', values=(f"{icon} {item}", size_str, file_type, modified))
                self.local_tree.set(item_id, 'name', f"{icon} {item}")

                # 设置颜色（如果支持）
                try:
                    self.local_tree.item(item_id, tags=(color_tag,))
                except:
                    pass
            
        except Exception as e:
            messagebox.showerror("错误", f"无法读取本地目录: {str(e)}")
    
    def refresh_remote(self):
        """刷新远程文件列表"""
        if not self.current_server:
            return
            
        def refresh_thread():
            self.update_status("正在刷新远程文件列表...")
            
            # 清空现有项目
            for item in self.remote_tree.get_children():
                self.remote_tree.delete(item)
            
            # 获取文件列表
            files = self.manager.list_files(self.current_server, self.current_remote_path)

            # 添加上级目录项 - 总是显示在最顶部
            self.remote_tree.insert('', 'end', values=('...', '', '📁', '上级目录'))
            
            # 添加到树形控件
            for file_info in files:
                is_dir = file_info['type'] == 'dir'
                icon = self.get_file_icon(file_info['name'], is_dir)
                color_tag = self.get_file_color(file_info['name'], is_dir)
                size = self.format_file_size(file_info['size']) if file_info['size'] else ""

                # 插入项目并设置颜色标签
                item_id = self.remote_tree.insert('', 'end', values=(
                    f"{icon} {file_info['name']}",
                    size,
                    file_info['permissions'],
                    file_info['mtime']
                ))

                # 设置颜色（如果支持）
                try:
                    self.remote_tree.item(item_id, tags=(color_tag,))
                except:
                    pass
            
            self.update_status(f"已加载 {len(files)} 个项目")
        
        threading.Thread(target=refresh_thread, daemon=True).start()
    
    def on_local_double_click(self, event):
        """本地文件双击事件"""
        selection = self.local_tree.selection()
        if not selection:
            return
            
        item = self.local_tree.item(selection[0])
        file_name_with_icon = item['values'][0]
        # 移除图标，获取真实文件名
        file_name = file_name_with_icon.split(' ', 1)[1] if ' ' in file_name_with_icon else file_name_with_icon
        
        if file_name == '..' or file_name == '...':
            # 返回上级目录
            parent_path = os.path.dirname(self.current_local_path)
            if parent_path != self.current_local_path:
                self.current_local_path = parent_path
                self.local_path_var.set(self.current_local_path)
                self.refresh_local()
        else:
            item_path = os.path.join(self.current_local_path, file_name)
            if os.path.isdir(item_path):
                # 进入目录
                self.current_local_path = item_path
                self.local_path_var.set(self.current_local_path)
                self.refresh_local()
    
    def on_remote_double_click(self, event):
        """远程文件双击事件"""
        if not self.current_server:
            return
            
        selection = self.remote_tree.selection()
        if not selection:
            return
            
        item = self.remote_tree.item(selection[0])
        file_name_with_icon = item['values'][0]
        # 移除图标，获取真实文件名
        file_name = file_name_with_icon.split(' ', 1)[1] if ' ' in file_name_with_icon else file_name_with_icon
        
        if file_name == '..' or file_name == '...':
            # 返回上级目录
            self.go_remote_parent()
        else:
            # 检查是否为目录
            file_type = item['values'][2]  # permissions列，目录以'd'开头
            if file_type.startswith('d') or file_type.startswith('📁'):
                # 进入目录
                self.current_remote_path = self.current_remote_path.rstrip('/') + '/' + file_name
                self.remote_path_var.set(self.current_remote_path)
                self.refresh_remote()
    
    def change_local_path(self, event=None):
        """改变本地路径"""
        new_path = self.local_path_var.get()
        if os.path.exists(new_path) and os.path.isdir(new_path):
            self.current_local_path = new_path
            self.refresh_local()
        else:
            messagebox.showerror("错误", "路径不存在或不是目录")
            self.local_path_var.set(self.current_local_path)
    
    def go_remote_home(self):
        """返回远程根目录"""
        if self.current_server:
            self.current_remote_path = self.manager.servers[self.current_server]['remote_path']
            self.remote_path_var.set(self.current_remote_path)
            self.refresh_remote()
    
    def go_remote_parent(self):
        """返回远程上级目录"""
        if self.current_server and self.current_remote_path:
            parent_path = '/'.join(self.current_remote_path.rstrip('/').split('/')[:-1])
            if not parent_path:
                parent_path = '/'

            # 允许在网站根目录范围内导航，但不能超出/www/wwwroot/
            min_path = '/www/wwwroot'
            if len(parent_path) >= len(min_path) and parent_path.startswith(min_path):
                self.current_remote_path = parent_path
                self.remote_path_var.set(self.current_remote_path)
                self.refresh_remote()
            else:
                self.update_status("已到达最上级目录")
    
    def create_local_folder(self):
        """创建本地文件夹"""
        folder_name = simpledialog.askstring("新建文件夹", "请输入文件夹名称:")
        if folder_name:
            try:
                folder_path = os.path.join(self.current_local_path, folder_name)
                os.makedirs(folder_path)
                self.refresh_local()
                messagebox.showinfo("成功", f"文件夹 '{folder_name}' 创建成功")
            except Exception as e:
                messagebox.showerror("错误", f"创建文件夹失败: {str(e)}")
    
    def create_remote_folder(self):
        """创建远程文件夹"""
        if not self.current_server:
            messagebox.showwarning("提示", "请先连接服务器")
            return
            
        folder_name = simpledialog.askstring("新建文件夹", "请输入文件夹名称:")
        if folder_name:
            def create_thread():
                remote_dir = self.current_remote_path.rstrip('/') + '/' + folder_name
                result = self.manager.create_directory(self.current_server, remote_dir)
                
                if result:
                    messagebox.showinfo("成功", f"文件夹 '{folder_name}' 创建成功")
                    self.refresh_remote()
                else:
                    messagebox.showerror("错误", "创建文件夹失败")
            
            threading.Thread(target=create_thread, daemon=True).start()
    
    def upload_selected(self):
        """上传选中的本地文件"""
        if not self.current_server:
            messagebox.showwarning("提示", "请先连接服务器")
            return
            
        selection = self.local_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要上传的文件")
            return
            
        item = self.local_tree.item(selection[0])
        file_name_with_icon = item['values'][0]
        file_name = self.extract_filename(file_name_with_icon)

        if file_name in ['..', '...']:
            return

        local_file = os.path.join(self.current_local_path, file_name)
        
        if os.path.isdir(local_file):
            messagebox.showwarning("提示", "暂不支持上传文件夹")
            return
            
        def upload_thread():
            self.update_status(f"正在上传: {file_name}")
            
            result = self.manager.upload_file(self.current_server, local_file, self.current_remote_path)
            if result:
                messagebox.showinfo("上传成功", f"文件上传成功！\n访问地址: {result}")
                self.refresh_remote()
            else:
                messagebox.showerror("上传失败", "文件上传失败！")
            
            self.update_status("就绪")
        
        threading.Thread(target=upload_thread, daemon=True).start()
    
    def download_selected(self):
        """下载选中的远程文件"""
        if not self.current_server:
            messagebox.showwarning("提示", "请先连接服务器")
            return
            
        selection = self.remote_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要下载的文件")
            return
            
        item = self.remote_tree.item(selection[0])
        file_name_with_icon = item['values'][0]
        file_name = self.extract_filename(file_name_with_icon)

        if file_name in ['..', '...']:
            return
            
        # 检查是否为目录
        file_type = item['values'][2]
        if file_type.startswith('d'):
            messagebox.showwarning("提示", "暂不支持下载文件夹")
            return
            
        def download_thread():
            self.update_status(f"正在下载: {file_name}")
            
            remote_file = self.current_remote_path.rstrip('/') + '/' + file_name
            local_file = os.path.join(self.current_local_path, file_name)
            
            result = self.manager.download_file(self.current_server, remote_file, local_file)
            
            if result:
                messagebox.showinfo("下载成功", f"文件已下载到: {local_file}")
                self.refresh_local()
            else:
                messagebox.showerror("下载失败", "文件下载失败！")
            
            self.update_status("就绪")
        
        threading.Thread(target=download_thread, daemon=True).start()
    
    def delete_selected(self):
        """删除选中的远程文件"""
        if not self.current_server:
            messagebox.showwarning("提示", "请先连接服务器")
            return
            
        selection = self.remote_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要删除的文件")
            return
            
        item = self.remote_tree.item(selection[0])
        file_name_with_icon = item['values'][0]
        file_name = self.extract_filename(file_name_with_icon)

        if file_name in ['..', '...']:
            return
            
        if not messagebox.askyesno("确认删除", f"确定要删除 '{file_name}' 吗？"):
            return
            
        def delete_thread():
            self.update_status(f"正在删除: {file_name}")
            
            remote_file = self.current_remote_path.rstrip('/') + '/' + file_name
            result = self.manager.delete_file(self.current_server, remote_file)
            
            if result:
                messagebox.showinfo("删除成功", f"文件 '{file_name}' 已删除")
                self.refresh_remote()
            else:
                messagebox.showerror("删除失败", "文件删除失败！")
            
            self.update_status("就绪")
        
        threading.Thread(target=delete_thread, daemon=True).start()
    
    def show_local_context_menu(self, event):
        """显示本地文件右键菜单"""
        selection = self.local_tree.selection()
        if not selection:
            return

        item = self.local_tree.item(selection[0])
        file_name_with_icon = item['values'][0]
        file_name = self.extract_filename(file_name_with_icon)

        if file_name in ['..', '...']:
            return

        # 创建右键菜单
        context_menu = tk.Menu(self.root, tearoff=0)

        local_file = os.path.join(self.current_local_path, file_name)
        if os.path.isfile(local_file):
            context_menu.add_command(label="📤 上传到服务器", command=self.upload_selected)
            context_menu.add_separator()
            context_menu.add_command(label="📝 重命名", command=self.rename_local_file)
            context_menu.add_command(label="🗑️ 删除", command=self.delete_local_file)
        else:
            context_menu.add_command(label="📂 进入文件夹", command=lambda: self.on_local_double_click(None))
            context_menu.add_separator()
            context_menu.add_command(label="📝 重命名", command=self.rename_local_file)
            context_menu.add_command(label="🗑️ 删除", command=self.delete_local_file)

        context_menu.add_separator()
        context_menu.add_command(label="📋 复制路径", command=lambda: self.copy_local_path(file_name))

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_remote_context_menu(self, event):
        """显示远程文件右键菜单"""
        if not self.current_server:
            return

        selection = self.remote_tree.selection()
        if not selection:
            return

        item = self.remote_tree.item(selection[0])
        file_name_with_icon = item['values'][0]
        file_name = self.extract_filename(file_name_with_icon)

        if file_name in ['..', '...']:
            return

        # 创建右键菜单
        context_menu = tk.Menu(self.root, tearoff=0)

        # 检查是否为目录
        file_type = item['values'][2]
        if file_type.startswith('d'):
            context_menu.add_command(label="📂 进入文件夹", command=lambda: self.on_remote_double_click(None))
            context_menu.add_separator()
        else:
            context_menu.add_command(label="📥 下载到本地", command=self.download_selected)
            context_menu.add_command(label="🌐 复制访问链接", command=lambda: self.copy_remote_url(file_name))
            context_menu.add_separator()

        context_menu.add_command(label="🗑️ 删除", command=self.delete_selected)
        context_menu.add_separator()
        context_menu.add_command(label="📋 复制路径", command=lambda: self.copy_remote_path(file_name))

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def rename_local_file(self):
        """重命名本地文件"""
        selection = self.local_tree.selection()
        if not selection:
            return

        item = self.local_tree.item(selection[0])
        old_name_with_icon = item['values'][0]
        old_name = self.extract_filename(old_name_with_icon)

        if old_name in ['..', '...']:
            return

        new_name = simpledialog.askstring("重命名", f"请输入新名称:", initialvalue=old_name)
        if new_name and new_name != old_name:
            try:
                old_path = os.path.join(self.current_local_path, old_name)
                new_path = os.path.join(self.current_local_path, new_name)
                os.rename(old_path, new_path)
                self.refresh_local()
                messagebox.showinfo("成功", f"已重命名为: {new_name}")
            except Exception as e:
                messagebox.showerror("错误", f"重命名失败: {str(e)}")

    def delete_local_file(self):
        """删除本地文件"""
        selection = self.local_tree.selection()
        if not selection:
            return

        item = self.local_tree.item(selection[0])
        file_name_with_icon = item['values'][0]
        file_name = self.extract_filename(file_name_with_icon)

        if file_name in ['..', '...']:
            return

        if not messagebox.askyesno("确认删除", f"确定要删除本地文件 '{file_name}' 吗？"):
            return

        try:
            file_path = os.path.join(self.current_local_path, file_name)
            if os.path.isfile(file_path):
                os.remove(file_path)
            else:
                import shutil
                shutil.rmtree(file_path)

            self.refresh_local()
            messagebox.showinfo("成功", f"已删除: {file_name}")
        except Exception as e:
            messagebox.showerror("错误", f"删除失败: {str(e)}")

    def copy_local_path(self, file_name):
        """复制本地文件路径到剪贴板"""
        file_path = os.path.join(self.current_local_path, file_name)
        self.root.clipboard_clear()
        self.root.clipboard_append(file_path)
        self.update_status(f"已复制路径: {file_path}")

    def copy_remote_path(self, file_name):
        """复制远程文件路径到剪贴板"""
        file_path = self.current_remote_path.rstrip('/') + '/' + file_name
        self.root.clipboard_clear()
        self.root.clipboard_append(file_path)
        self.update_status(f"已复制路径: {file_path}")

    def copy_remote_url(self, file_name):
        """复制远程文件访问URL到剪贴板"""
        if not self.current_server:
            return

        # 生成访问URL
        relative_path = (self.current_remote_path.rstrip('/') + '/' + file_name).replace(
            self.manager.servers[self.current_server]['remote_path'], '')
        access_url = self.manager.servers[self.current_server]['url_base'] + relative_path.lstrip('/')

        self.root.clipboard_clear()
        self.root.clipboard_append(access_url)
        self.update_status(f"已复制访问链接: {access_url}")
        messagebox.showinfo("复制成功", f"访问链接已复制到剪贴板:\n{access_url}")

    def breathing_animation(self):
        """呼吸灯动画效果"""
        import time
        current_time = time.time()
        # 使用正弦波创建呼吸效果
        alpha = int(128 + 127 * abs(((current_time * 2) % 4) - 2) / 2)

        # 更新连接状态的颜色
        try:
            if hasattr(self, 'connection_var'):
                if "🟢" in self.connection_var.get():
                    # 连接状态时的呼吸效果
                    color = f"#{alpha:02x}ff{alpha:02x}"  # 青色呼吸
                else:
                    color = "#0ef"  # 默认青色
        except:
            pass

        # 每100毫秒更新一次
        self.root.after(100, self.breathing_animation)

    def on_closing(self):
        """关闭程序"""
        self.manager.close_all_connections()
        self.root.destroy()

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SSHFTPManager()
    app.run()
