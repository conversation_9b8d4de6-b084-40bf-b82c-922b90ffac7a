#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - 增强版FTP风格可执行程序构建
包含文件图标、颜色区分、列排序、桌面快捷等功能
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def create_enhanced_ftp_spec():
    """创建增强版FTP风格PyInstaller配置"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ssh_ftp_manager.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ssh_file_manager.py', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'paramiko',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.simpledialog',
        'threading',
        'subprocess',
        'datetime',
        'pathlib',
        'stat',
        'os',
        'sys',
        'shutil'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'PIL'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='项老师SSH文件管理器_增强版_v1.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.ico' if os.path.exists('logo.ico') else None,
)
'''
    
    with open('ssh_enhanced_ftp.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 增强版FTP风格配置文件创建完成")

def build_enhanced_ftp_exe():
    """构建增强版FTP风格可执行文件"""
    print("🎯 项老师SSH文件管理器 - 增强版FTP风格构建")
    print("=" * 60)
    
    try:
        # 清理之前的构建
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # 创建配置文件
        create_enhanced_ftp_spec()
        
        print("🔨 开始构建增强版FTP风格可执行文件...")
        
        # 构建
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'ssh_enhanced_ftp.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 增强版FTP风格构建成功！")
            
            # 创建发布目录
            build_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            release_dir = f"项老师SSH文件管理器_增强版_{build_time}"
            
            if os.path.exists(release_dir):
                shutil.rmtree(release_dir)
            os.makedirs(release_dir)
            
            # 复制可执行文件
            exe_path = "dist/项老师SSH文件管理器_增强版_v1.0.exe"
            if os.path.exists(exe_path):
                shutil.copy2(exe_path, release_dir)
                print(f"✅ 增强版已复制到 {release_dir}")
            
            # 复制说明文档
            if os.path.exists('README.md'):
                shutil.copy2('README.md', release_dir)
            
            # 创建增强版功能说明
            enhanced_guide = f'''# 🎯 项老师SSH文件管理器 - 增强版

## 🚀 快速开始
双击运行: 项老师SSH文件管理器_增强版_v1.0.exe

## ✨ 全新增强功能

### 🎨 文件图标与颜色区分
✅ **丰富图标系统** - 不同文件类型显示专属图标
- 📁 文件夹 (金色)
- 🖼️ 图片文件 (粉色) - jpg, png, gif等
- 🌐 网页文件 (绿色) - html, css, js等  
- 📝 文档文件 (蓝色) - txt, doc, pdf等
- 📦 压缩文件 (紫色) - zip, rar, 7z等
- 🎵 音频文件 (橙色) - mp3, wav等
- 🎬 视频文件 (红色) - mp4, avi等
- ⚡ 可执行文件 (红橙色) - exe, msi等

### 📊 智能列排序
✅ **点击列标题排序** - 所有列都支持点击排序
- **文件名**: 按字母顺序排序，文件夹优先
- **大小**: 按文件大小排序，大文件在前
- **修改时间**: 按时间排序，最新文件在前
- **类型/权限**: 按文件类型排序

### 🖥️ 桌面快捷功能
✅ **一键到桌面** - 本地面板新增"🖥️ 桌面"按钮
- 快速跳转到用户桌面目录
- 支持中英文桌面路径自动识别
- 方便快速上传桌面文件

### 📁 三点导航优化
✅ **文件列表顶部"..."** - 更直观的上级目录导航
- 始终显示在文件列表最顶部
- 点击快速返回上级目录
- 支持本地和远程双面板

## 🌟 完整功能特性

### 🖥️ 专业双面板设计
- **左侧面板**: 本地文件浏览器 + 桌面快捷
- **右侧面板**: 远程文件浏览器 (网站根目录)
- **同步操作**: 左右面板独立操作，实时同步

### 📁 完整目录导航
✅ **多种导航方式**:
- 三点上级: 文件列表顶部"..."
- 双击进入: 双击文件夹直接进入
- 根目录按钮: 快速返回网站根目录
- 地址栏跳转: 直接输入路径快速跳转
- 桌面快捷: 一键跳转到桌面

### 🔄 完整文件操作
✅ **双向传输**: 本地↔远程文件上传下载
✅ **文件管理**: 新建、删除、重命名文件夹
✅ **右键菜单**: 丰富的上下文操作菜单
✅ **访问链接**: 一键生成文件直接访问URL
✅ **批量操作**: 支持多文件选择和操作

### 🎯 用户体验优化
✅ **视觉识别**: 文件图标和颜色让文件类型一目了然
✅ **快速排序**: 点击任意列标题即可排序
✅ **便捷导航**: 桌面按钮让文件上传更方便
✅ **直观操作**: 三点导航符合用户习惯

## 🎮 操作指南

### 连接服务器
1. 点击顶部"调研报告服务器"或"说明文档服务器"
2. 连接成功后右侧显示网站根目录文件
3. 左侧显示本地文件，可自由浏览

### 文件识别
- **图标识别**: 每种文件类型都有专属图标
- **颜色区分**: 不同类型文件显示不同颜色
- **快速定位**: 通过颜色和图标快速找到目标文件

### 列表排序
- **点击列标题**: 任意列标题都可点击排序
- **智能排序**: 文件夹总是排在文件前面
- **多次点击**: 可在升序降序间切换

### 快捷导航
- **桌面按钮**: 点击"🖥️ 桌面"快速跳转
- **三点返回**: 点击文件列表顶部"..."返回上级
- **地址栏**: 直接输入路径快速跳转

### 文件操作
- **上传**: 选中本地文件，点击"📤 上传"
- **下载**: 选中远程文件，点击"📥 下载"
- **右键菜单**: 右键任何文件获取完整操作选项

## 🔐 服务器配置

### 服务器1 - 调研报告服务器
- **地址**: 8.138.203.25
- **根目录**: /www/wwwroot/www.diwangzhidao.com/
- **网站**: https://www.diwangzhidao.com/

### 服务器2 - 说明文档服务器
- **地址**: 39.97.189.146
- **根目录**: /www/wwwroot/www.xli8.com/
- **网站**: http://www.xli8.com/

## 💡 使用技巧

### 文件管理技巧
- **颜色识别**: 通过颜色快速识别文件类型
- **图标区分**: 图标让文件类型更直观
- **排序查找**: 点击列标题快速排序查找文件
- **桌面上传**: 使用桌面按钮快速上传桌面文件

### 导航技巧
- **三点快捷**: "..."是最快的返回上级方式
- **地址栏**: 可以直接粘贴完整路径快速跳转
- **根目录**: 迷路时点击"🏠 根目录"快速回到起点

### 效率提升
- **双面板**: 同时管理本地和远程文件
- **右键菜单**: 右键获取所有可用操作
- **访问链接**: 远程文件右键直接复制访问URL

## 🛡️ 安全特性
- **SSH+SFTP加密**: 所有数据传输完全加密
- **权限控制**: 自动设置合适的文件权限
- **安全范围**: 限制在网站目录范围内
- **操作确认**: 删除等危险操作需要确认

## 📦 系统要求
- **操作系统**: Windows 7/8/10/11
- **架构**: x64
- **依赖**: 无需安装Python或其他环境
- **网络**: 需要能访问服务器的网络环境

## 🎉 版本亮点

这是项老师SSH文件管理器的终极增强版，具有以下突出特色：

✅ **专业视觉体验** - 丰富的文件图标和颜色系统
✅ **智能排序功能** - 点击任意列标题即可排序
✅ **便捷桌面访问** - 一键跳转到桌面目录
✅ **完美FTP体验** - 完全模拟专业FTP客户端
✅ **网站根目录** - 直接从网站根目录开始管理
✅ **三点导航** - 直观的"..."上级目录导航

这是一个真正专业级、功能完整、体验优秀的SSH文件管理工具！

---
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
© 2025 项老师AI工作室 | 传统企业AI自动化转型专家
'''
            
            with open(f'{release_dir}/增强版功能说明.txt', 'w', encoding='utf-8') as f:
                f.write(enhanced_guide)
            
            # 创建快速启动批处理
            batch_content = f'''@echo off
chcp 65001 >nul
title 项老师SSH文件管理器 - 增强版

echo.
echo 🎯 项老师SSH文件管理器 - 增强版
echo.
echo ✨ 全新增强功能:
echo    • 🎨 文件图标与颜色区分
echo    • 📊 智能列排序功能
echo    • 🖥️ 桌面快捷按钮
echo    • 📁 三点导航优化
echo.
echo 正在启动专业FTP风格界面...
echo.

"项老师SSH文件管理器_增强版_v1.0.exe"

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败，请检查：
    echo 1. 是否被杀毒软件拦截
    echo 2. 是否有足够的系统权限
    echo 3. 系统是否支持图形界面
    echo.
    pause
)
'''
            
            with open(f'{release_dir}/启动SSH管理器增强版.bat', 'w', encoding='utf-8') as f:
                f.write(batch_content)
            
            print(f"🎉 增强版FTP风格构建完成: {release_dir}")
            
            # 显示文件大小
            exe_size = os.path.getsize(f"{release_dir}/项老师SSH文件管理器_增强版_v1.0.exe") / (1024*1024)
            print(f"📦 文件大小: {exe_size:.1f} MB")
            
            return True
            
        else:
            print(f"❌ 构建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = build_enhanced_ftp_exe()
    
    if success:
        print("\n✅ 增强版SSH文件管理器构建成功！")
        print("💡 包含文件图标、颜色区分、列排序、桌面快捷等功能")
        print("🚀 这是功能最完整、体验最佳的终极版本")
        print("🎯 强烈推荐作为主要使用版本")
    else:
        print("\n❌ 增强版构建失败")
    
    input("\n按回车键退出...")
