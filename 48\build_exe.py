#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - 独立可执行程序打包工具
使用PyInstaller将Python程序打包为exe文件
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

class ExeBuilder:
    def __init__(self):
        self.version = "v1.0"
        self.build_time = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.app_name = f"项老师SSH文件管理器_{self.version}"
        
    def check_pyinstaller(self):
        """检查并安装PyInstaller"""
        try:
            import PyInstaller
            print("✅ PyInstaller已安装")
            return True
        except ImportError:
            print("📦 正在安装PyInstaller...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
                print("✅ PyInstaller安装成功")
                return True
            except subprocess.CalledProcessError:
                print("❌ PyInstaller安装失败")
                return False
    
    def create_main_launcher(self):
        """创建主启动器"""
        launcher_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - 主启动器
独立可执行版本
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
import threading

class SSHManagerLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("项老师SSH文件管理器 v1.0")
        self.root.geometry("600x400")
        self.root.configure(bg='#2b2b2b')
        self.root.resizable(False, False)
        
        # 设置图标
        try:
            # 如果有logo文件，设置为图标
            if os.path.exists('logo.ico'):
                self.root.iconbitmap('logo.ico')
        except:
            pass
        
        self.create_widgets()
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (400 // 2)
        self.root.geometry(f"600x400+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 标题区域
        title_frame = tk.Frame(self.root, bg='#2b2b2b', height=100)
        title_frame.pack(fill='x', padx=20, pady=20)
        title_frame.pack_propagate(False)
        
        # 主标题
        title_label = tk.Label(title_frame, 
                              text="🎯 项老师SSH文件管理器",
                              bg='#2b2b2b', fg='#ffffff',
                              font=('Microsoft YaHei', 20, 'bold'))
        title_label.pack(pady=10)
        
        # 副标题
        subtitle_label = tk.Label(title_frame,
                                 text="安全 • 高效 • 专业的文件管理解决方案",
                                 bg='#2b2b2b', fg='#cccccc',
                                 font=('Microsoft YaHei', 12))
        subtitle_label.pack()
        
        # 功能按钮区域
        button_frame = tk.Frame(self.root, bg='#2b2b2b')
        button_frame.pack(fill='both', expand=True, padx=40, pady=20)
        
        # 按钮样式
        button_style = {
            'font': ('Microsoft YaHei', 12, 'bold'),
            'width': 25,
            'height': 2,
            'relief': 'flat',
            'cursor': 'hand2'
        }
        
        # 图形界面版按钮
        gui_btn = tk.Button(button_frame,
                           text="🖥️ 图形界面版 (推荐)",
                           bg='#4CAF50', fg='white',
                           command=self.launch_gui,
                           **button_style)
        gui_btn.pack(pady=10, fill='x')
        
        # 命令行版按钮
        cli_btn = tk.Button(button_frame,
                           text="💻 命令行版 (专业)",
                           bg='#2196F3', fg='white',
                           command=self.launch_cli,
                           **button_style)
        cli_btn.pack(pady=10, fill='x')
        
        # 快速上传按钮
        upload_btn = tk.Button(button_frame,
                              text="📤 快速上传工具",
                              bg='#FF9800', fg='white',
                              command=self.launch_upload,
                              **button_style)
        upload_btn.pack(pady=10, fill='x')
        
        # 测试连接按钮
        test_btn = tk.Button(button_frame,
                            text="🔍 测试服务器连接",
                            bg='#9C27B0', fg='white',
                            command=self.test_connection,
                            **button_style)
        test_btn.pack(pady=10, fill='x')
        
        # 底部信息
        info_frame = tk.Frame(self.root, bg='#2b2b2b', height=60)
        info_frame.pack(fill='x', side='bottom')
        info_frame.pack_propagate(False)
        
        info_label = tk.Label(info_frame,
                             text="© 2025 项老师AI工作室 | 传统企业AI自动化转型专家",
                             bg='#2b2b2b', fg='#888888',
                             font=('Microsoft YaHei', 10))
        info_label.pack(pady=20)
    
    def launch_gui(self):
        """启动图形界面版"""
        try:
            from ssh_gui_manager import SSHGUIManager
            self.root.withdraw()  # 隐藏启动器
            app = SSHGUIManager()
            app.run()
            self.root.deiconify()  # 显示启动器
        except Exception as e:
            messagebox.showerror("启动失败", f"图形界面版启动失败:\\n{str(e)}")
    
    def launch_cli(self):
        """启动命令行版"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, "ssh_cli_manager.py"], 
                           creationflags=subprocess.CREATE_NEW_CONSOLE)
        except Exception as e:
            messagebox.showerror("启动失败", f"命令行版启动失败:\\n{str(e)}")
    
    def launch_upload(self):
        """启动快速上传"""
        try:
            from quick_upload import quick_upload
            threading.Thread(target=quick_upload, daemon=True).start()
        except Exception as e:
            messagebox.showerror("启动失败", f"快速上传工具启动失败:\\n{str(e)}")
    
    def test_connection(self):
        """测试连接"""
        def test_thread():
            try:
                from ssh_file_manager import SSHFileManager
                manager = SSHFileManager()
                
                results = []
                for server_name, config in manager.servers.items():
                    ssh, sftp = manager.connect_server(server_name)
                    if ssh and sftp:
                        results.append(f"✅ {config['name']}: 连接成功")
                        manager.disconnect_server(server_name)
                    else:
                        results.append(f"❌ {config['name']}: 连接失败")
                
                result_text = "\\n".join(results)
                messagebox.showinfo("连接测试结果", result_text)
                
            except Exception as e:
                messagebox.showerror("测试失败", f"连接测试失败:\\n{str(e)}")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def run(self):
        """运行启动器"""
        self.root.mainloop()

if __name__ == "__main__":
    # 导入所有必要的模块
    try:
        from ssh_file_manager import SSHFileManager
        from ssh_gui_manager import SSHGUIManager
        import paramiko
    except ImportError as e:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        tk.messagebox.showerror("依赖缺失", f"缺少必要的依赖包:\\n{str(e)}\\n\\n请安装: pip install paramiko")
        sys.exit(1)
    
    launcher = SSHManagerLauncher()
    launcher.run()
'''
        
        with open('ssh_manager_launcher.py', 'w', encoding='utf-8') as f:
            f.write(launcher_code)
        
        print("✅ 主启动器创建完成")
    
    def create_spec_file(self):
        """创建PyInstaller配置文件"""
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ssh_manager_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ssh_file_manager.py', '.'),
        ('ssh_gui_manager.py', '.'),
        ('ssh_cli_manager.py', '.'),
        ('quick_upload.py', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'paramiko',
        'tkinter',
        'threading',
        'subprocess',
        'datetime',
        'pathlib',
        'stat',
        'os',
        'sys'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.ico' if os.path.exists('logo.ico') else None,
    version_file='version_info.txt' if os.path.exists('version_info.txt') else None,
)
'''
        
        with open('ssh_manager.spec', 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print("✅ PyInstaller配置文件创建完成")
    
    def create_version_info(self):
        """创建版本信息文件"""
        version_info = f'''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404B0',
        [StringStruct(u'CompanyName', u'项老师AI工作室'),
        StringStruct(u'FileDescription', u'SSH文件管理器'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'ssh_manager'),
        StringStruct(u'LegalCopyright', u'© 2025 项老师AI工作室'),
        StringStruct(u'OriginalFilename', u'{self.app_name}.exe'),
        StringStruct(u'ProductName', u'项老师SSH文件管理器'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
'''
        
        with open('version_info.txt', 'w', encoding='utf-8') as f:
            f.write(version_info)
        
        print("✅ 版本信息文件创建完成")
    
    def download_logo(self):
        """下载项老师logo作为图标"""
        try:
            import requests
            from PIL import Image
            
            print("📥 正在下载项老师logo...")
            response = requests.get('https://www.diwangzhidao.com/logo.png', timeout=10)
            
            if response.status_code == 200:
                with open('logo.png', 'wb') as f:
                    f.write(response.content)
                
                # 转换为ico格式
                img = Image.open('logo.png')
                img.save('logo.ico', format='ICO', sizes=[(32,32), (48,48), (64,64)])
                
                print("✅ Logo下载并转换完成")
                return True
            else:
                print("⚠️ Logo下载失败，使用默认图标")
                return False
                
        except Exception as e:
            print(f"⚠️ Logo处理失败: {str(e)}")
            return False
    
    def build_executable(self):
        """构建可执行文件"""
        print("🔨 开始构建可执行文件...")
        
        try:
            # 使用PyInstaller构建
            cmd = [
                'pyinstaller',
                '--clean',
                '--noconfirm',
                'ssh_manager.spec'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print("✅ 可执行文件构建成功！")
                return True
            else:
                print(f"❌ 构建失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 构建过程出错: {str(e)}")
            return False
    
    def create_portable_package(self):
        """创建便携版打包"""
        print("📦 创建便携版打包...")
        
        try:
            # 创建发布目录
            release_dir = f"{self.app_name}_{self.build_time}"
            if os.path.exists(release_dir):
                shutil.rmtree(release_dir)
            os.makedirs(release_dir)
            
            # 复制可执行文件
            exe_path = f"dist/{self.app_name}.exe"
            if os.path.exists(exe_path):
                shutil.copy2(exe_path, release_dir)
                print(f"✅ 可执行文件已复制到 {release_dir}")
            
            # 复制说明文档
            docs = ['README.md', '项目完成报告.md']
            for doc in docs:
                if os.path.exists(doc):
                    shutil.copy2(doc, release_dir)
            
            # 创建快速启动说明
            quick_start = f'''# 🎯 项老师SSH文件管理器 - 快速启动指南

## 🚀 立即使用
双击运行: {self.app_name}.exe

## 📋 功能说明
- 🖥️ 图形界面版: 可视化文件管理
- 💻 命令行版: 专业命令行操作  
- 📤 快速上传: 拖拽文件上传
- 🔍 连接测试: 检查服务器状态

## 🔐 服务器信息
- 服务器1: 调研报告服务器 (8.138.203.25)
- 服务器2: 说明文档服务器 (39.97.189.146)

## 💡 使用提示
1. 首次使用建议先进行连接测试
2. 图形界面版适合日常使用
3. 命令行版适合批量操作
4. 支持中文文件名，自动生成访问链接

---
© 2025 项老师AI工作室
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
'''
            
            with open(f'{release_dir}/快速启动说明.txt', 'w', encoding='utf-8') as f:
                f.write(quick_start)
            
            print(f"✅ 便携版打包完成: {release_dir}")
            return release_dir
            
        except Exception as e:
            print(f"❌ 打包失败: {str(e)}")
            return None
    
    def build(self):
        """执行完整构建流程"""
        print("🎯 项老师SSH文件管理器 - 独立可执行程序构建")
        print("=" * 60)
        
        # 1. 检查PyInstaller
        if not self.check_pyinstaller():
            return False
        
        # 2. 安装必要依赖
        print("📦 检查依赖包...")
        dependencies = ['paramiko', 'pillow', 'requests']
        for dep in dependencies:
            try:
                __import__(dep)
                print(f"✅ {dep} 已安装")
            except ImportError:
                print(f"📦 正在安装 {dep}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
        
        # 3. 下载logo
        self.download_logo()
        
        # 4. 创建文件
        self.create_main_launcher()
        self.create_spec_file()
        self.create_version_info()
        
        # 5. 构建可执行文件
        if not self.build_executable():
            return False
        
        # 6. 创建便携版
        release_dir = self.create_portable_package()
        
        if release_dir:
            print("\n🎉 构建完成！")
            print("=" * 60)
            print(f"📁 发布目录: {release_dir}")
            print(f"🚀 可执行文件: {self.app_name}.exe")
            print("💡 双击exe文件即可运行，无需安装Python环境")
            return True
        else:
            return False

if __name__ == "__main__":
    builder = ExeBuilder()
    success = builder.build()
    
    if success:
        print("\n✅ 独立可执行程序构建成功！")
    else:
        print("\n❌ 构建失败，请检查错误信息")
    
    input("\n按回车键退出...")
