# 🎯 项老师SSH文件管理器 - FTP风格完整版

## 🚀 快速开始
双击运行: 项老师SSH文件管理器_FTP风格_v1.0.exe

## 🌟 功能特点
✅ **双面板设计** - 左侧本地文件，右侧远程文件
✅ **完整目录导航** - 支持进入任意文件夹，返回上级目录
✅ **双向文件传输** - 本地↔远程文件上传下载
✅ **右键菜单** - 丰富的右键操作菜单
✅ **文件管理** - 重命名、删除、新建文件夹
✅ **路径导航** - 地址栏直接输入路径跳转
✅ **访问链接** - 自动生成远程文件访问URL

## 🖥️ 界面布局

### 左侧面板 - 本地文件
- 📁 本地文件浏览器
- 🔄 刷新按钮
- 📁 新建文件夹
- 📤 上传选中文件
- 📍 路径导航栏

### 右侧面板 - 远程文件  
- 🌐 远程文件浏览器
- 🔄 刷新按钮
- 📁 新建文件夹
- 📥 下载选中文件
- 🗑️ 删除选中文件
- 🏠 返回根目录
- ⬆️ 返回上级目录

## 🎯 操作指南

### 连接服务器
1. 点击顶部服务器按钮连接
2. 连接成功后右侧显示远程文件
3. 可随时点击"断开连接"

### 文件导航
- **双击文件夹**: 进入该文件夹
- **双击".."**: 返回上级目录
- **地址栏**: 直接输入路径按回车跳转
- **根目录按钮**: 快速返回服务器根目录

### 文件操作
- **上传**: 选中本地文件，点击"上传"或右键菜单
- **下载**: 选中远程文件，点击"下载"或右键菜单
- **删除**: 右键选择删除（支持本地和远程）
- **重命名**: 右键选择重命名（本地文件）
- **新建文件夹**: 点击对应面板的"新建文件夹"按钮

### 右键菜单功能
**本地文件右键**:
- 📤 上传到服务器
- 📝 重命名
- 🗑️ 删除
- 📋 复制路径

**远程文件右键**:
- 📥 下载到本地
- 🌐 复制访问链接
- 🗑️ 删除
- 📋 复制路径

## 🔐 服务器信息
- **服务器1**: 调研报告服务器 (8.138.203.25)
- **服务器2**: 说明文档服务器 (39.97.189.146)

## 💡 使用技巧
1. **批量操作**: 选中文件后使用按钮或右键菜单
2. **路径跳转**: 在地址栏输入完整路径快速跳转
3. **访问链接**: 右键远程文件可复制直接访问链接
4. **双面板**: 左右面板独立操作，方便文件管理

## 🛡️ 安全特性
- SSH+SFTP加密传输
- 自动连接管理
- 完善的错误处理
- 操作确认机制

## 📦 系统要求
- Windows 7/8/10/11
- 无需安装Python环境
- 支持中文文件名

---
构建时间: 2025-07-31 00:44:03
© 2025 项老师AI工作室 | 传统企业AI自动化转型专家
