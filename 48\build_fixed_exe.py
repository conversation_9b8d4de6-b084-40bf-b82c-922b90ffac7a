#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - 修复版独立可执行程序构建
修复Tkinter anchor参数错误
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def quick_build():
    """快速重新构建修复版"""
    print("🔧 正在重新构建修复版可执行程序...")
    
    try:
        # 清理之前的构建
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # 重新构建
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'ssh_manager.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 修复版构建成功！")
            
            # 创建新的发布目录
            build_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            release_dir = f"项老师SSH文件管理器_v1.0_修复版_{build_time}"
            
            if os.path.exists(release_dir):
                shutil.rmtree(release_dir)
            os.makedirs(release_dir)
            
            # 复制可执行文件
            exe_path = "dist/项老师SSH文件管理器_v1.0.exe"
            if os.path.exists(exe_path):
                shutil.copy2(exe_path, release_dir)
                print(f"✅ 修复版已复制到 {release_dir}")
            
            # 复制说明文档
            docs = ['README.md', '项目完成报告.md']
            for doc in docs:
                if os.path.exists(doc):
                    shutil.copy2(doc, release_dir)
            
            # 创建修复说明
            fix_notes = f'''# 🔧 修复版说明

## 修复内容
- ✅ 修复了Tkinter anchor参数错误
- ✅ 优化了图形界面显示
- ✅ 提升了程序稳定性

## 使用方法
双击运行: 项老师SSH文件管理器_v1.0.exe

## 修复时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

---
© 2025 项老师AI工作室
'''
            
            with open(f'{release_dir}/修复说明.txt', 'w', encoding='utf-8') as f:
                f.write(fix_notes)
            
            print(f"🎉 修复版构建完成: {release_dir}")
            return True
            
        else:
            print(f"❌ 构建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("🎯 项老师SSH文件管理器 - 修复版构建")
    print("=" * 50)
    
    success = quick_build()
    
    if success:
        print("\n✅ 修复版独立可执行程序构建成功！")
        print("💡 现在可以正常运行图形界面了")
    else:
        print("\n❌ 修复版构建失败")
    
    input("\n按回车键退出...")
