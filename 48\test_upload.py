#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSH上传测试示例
"""

from ssh_upload_tool import SSHUploader
import os
from datetime import datetime

def test_upload():
    """测试上传功能"""
    uploader = SSHUploader()
    
    # 创建一个测试文件
    test_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSH上传测试</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }}
        .container {{
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }}
        h1 {{
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(255,255,255,0.5);
        }}
        .info {{
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }}
        .success {{
            color: #4CAF50;
            font-weight: bold;
            font-size: 18px;
            text-align: center;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 SSH上传测试成功！</h1>
        
        <div class="info">
            <h3>📊 上传信息</h3>
            <p><strong>上传时间：</strong>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>上传方式：</strong>SSH + SFTP</p>
            <p><strong>文件大小：</strong>约2KB</p>
            <p><strong>服务器：</strong>项老师专用服务器</p>
        </div>
        
        <div class="info">
            <h3>✨ 功能特点</h3>
            <ul>
                <li>✅ 全自动SSH连接</li>
                <li>✅ 无需手动输入密码</li>
                <li>✅ 支持文件和目录上传</li>
                <li>✅ 自动生成访问链接</li>
                <li>✅ 错误处理和重试机制</li>
            </ul>
        </div>
        
        <div class="success">
            🚀 SSH上传工具运行正常！
        </div>
        
        <div class="info">
            <h3>🔗 相关链接</h3>
            <p>项老师AI工作室：<a href="https://www.diwangzhidao.com" style="color: #FFD700;">www.diwangzhidao.com</a></p>
        </div>
    </div>
</body>
</html>"""
    
    # 保存测试文件
    test_filename = f"ssh_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    with open(test_filename, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"📝 创建测试文件: {test_filename}")
    
    # 上传到服务器1
    print("\n🚀 上传到服务器1...")
    url1 = uploader.upload_file(test_filename, 'server1')
    
    # 上传到服务器2  
    print("\n🚀 上传到服务器2...")
    url2 = uploader.upload_file(test_filename, 'server2')
    
    # 清理本地测试文件
    os.remove(test_filename)
    print(f"🗑️ 清理本地文件: {test_filename}")
    
    print("\n" + "="*50)
    print("🎯 上传结果汇总")
    print("="*50)
    if url1:
        print(f"✅ 服务器1: {url1}")
    if url2:
        print(f"✅ 服务器2: {url2}")
    print("="*50)

if __name__ == "__main__":
    test_upload()
