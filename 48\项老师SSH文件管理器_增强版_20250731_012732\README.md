# 🎯 项老师SSH文件管理器

基于SSH+SFTP的完整文件管理解决方案，支持2个服务器的文件查看、上传、下载、删除等全部功能。

## 📋 功能特点

✅ **双服务器支持** - 同时管理2个服务器  
✅ **完整文件操作** - 查看、上传、下载、删除、创建目录  
✅ **多种界面** - 图形界面版、命令行版、快速上传版  
✅ **安全连接** - SSH+SFTP加密传输  
✅ **自动生成链接** - 上传后自动生成访问URL  
✅ **编码兼容** - 完美处理中文文件名  

## 🚀 快速开始

### 方法1：一键启动（推荐）
双击运行 `启动SSH文件管理器.bat`，选择需要的版本：

```
1. 图形界面版 (推荐) - 可视化操作
2. 命令行版 - 专业用户  
3. 测试连接 - 检查服务器状态
4. 快速上传 - 拖拽上传文件
```

### 方法2：直接运行
```bash
# 图形界面版
python ssh_gui_manager.py

# 命令行版
python ssh_cli_manager.py

# 快速上传
python quick_upload.py

# 测试连接
python ssh_file_manager.py
```

## 📁 文件说明

| 文件名 | 功能描述 |
|--------|----------|
| `ssh_file_manager.py` | 核心文件管理类 |
| `ssh_gui_manager.py` | 图形界面版本 |
| `ssh_cli_manager.py` | 命令行交互版本 |
| `quick_upload.py` | 快速上传工具 |
| `启动SSH文件管理器.bat` | 一键启动器 |

## 🖥️ 界面预览

### 图形界面版
- 🎨 现代化深色主题界面
- 📁 树形文件列表显示
- 🔄 实时状态更新
- 🖱️ 双击进入目录
- 📤 拖拽上传支持

### 命令行版
- 💻 专业命令行界面
- 📋 交互式菜单操作
- 🔢 数字快速选择
- 📊 详细文件信息显示
- ⚡ 高效批量操作

## 🔧 服务器配置

### 服务器1 - 调研报告服务器
- **地址**: 8.138.203.25
- **用途**: 调研报告和PHP文件
- **访问**: https://www.diwangzhidao.com/MCP/xiangliang/1/

### 服务器2 - 说明文档服务器  
- **地址**: 39.97.189.146
- **用途**: 说明文档和工具
- **访问**: http://www.xli8.com/shuomingwendang/

## 📤 使用示例

### 上传文件
```python
from ssh_file_manager import SSHFileManager

manager = SSHFileManager()
url = manager.upload_file('server1', 'local_file.txt')
print(f"访问地址: {url}")
```

### 下载文件
```python
manager.download_file('server1', '/remote/path/file.txt', 'local_file.txt')
```

### 列出文件
```python
files = manager.list_files('server1', '/remote/path/')
for file in files:
    print(f"{file['name']} - {file['size']} bytes")
```

## 🛡️ 安全特性

- **SSH加密** - 所有数据传输都经过SSH加密
- **自动重连** - 连接断开时自动重新连接
- **权限管理** - 自动设置合适的文件权限
- **错误处理** - 完善的异常处理机制

## 🔍 故障排除

### 连接失败
1. 检查网络连接
2. 确认服务器地址和端口
3. 验证用户名和密码

### 编码问题
- 工具已自动处理中文文件名编码问题
- 支持UTF-8和GBK编码自动识别

### 权限问题
- 确保SSH用户有目标目录的读写权限
- 上传的文件会自动设置为644权限

## 📞 技术支持

如遇问题，请检查：
1. Python环境是否正确安装
2. paramiko依赖是否已安装：`pip install paramiko`
3. 服务器SSH服务是否正常运行
4. 防火墙是否允许SSH连接

## 🎉 更新日志

### v1.0 (2025-07-30)
- ✅ 完整的SSH+SFTP文件管理功能
- ✅ 图形界面和命令行双版本
- ✅ 双服务器支持
- ✅ 中文文件名完美支持
- ✅ 自动URL生成功能

---

**© 2025 项老师AI工作室 | SSH文件管理器**
