#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - 简化版
专为独立可执行程序设计的简单版本
"""

import os
import sys
import time
from ssh_file_manager import SSHFileManager

class SimpleSSHManager:
    def __init__(self):
        self.manager = SSHFileManager()
        self.current_server = None
        
    def show_banner(self):
        """显示程序横幅"""
        print("=" * 60)
        print("🎯 项老师SSH文件管理器 v1.0")
        print("=" * 60)
        print("安全 • 高效 • 专业的文件管理解决方案")
        print("支持双服务器，完整文件操作功能")
        print("=" * 60)
    
    def show_servers(self):
        """显示服务器选择"""
        print("\n📡 请选择服务器:")
        print("-" * 40)
        for i, (server_name, config) in enumerate(self.manager.servers.items(), 1):
            print(f"{i}. {config['name']}")
            print(f"   地址: {config['host']}")
            print(f"   用途: {'调研报告和PHP文件' if i == 1 else '说明文档和工具'}")
            print()
    
    def connect_and_list(self, server_choice):
        """连接服务器并列出文件"""
        server_names = list(self.manager.servers.keys())
        
        if server_choice == '1':
            server_name = server_names[0]
        elif server_choice == '2':
            server_name = server_names[1]
        else:
            print("❌ 无效选择")
            return False
            
        print(f"\n🔗 正在连接到 {self.manager.servers[server_name]['name']}...")
        
        ssh, sftp = self.manager.connect_server(server_name)
        if ssh and sftp:
            self.current_server = server_name
            print(f"✅ 连接成功！")
            
            # 列出文件
            files = self.manager.list_files(server_name)
            return True
        else:
            print("❌ 连接失败")
            return False
    
    def upload_file_simple(self):
        """简单上传文件"""
        if not self.current_server:
            print("❌ 请先连接服务器")
            return
            
        print("\n📤 文件上传功能")
        print("-" * 30)
        
        while True:
            file_path = input("请输入文件路径 (或输入 'q' 退出): ").strip().strip('"')
            
            if file_path.lower() == 'q':
                break
                
            if not file_path:
                print("❌ 请输入文件路径")
                continue
                
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                continue
            
            print(f"🚀 正在上传: {os.path.basename(file_path)}")
            
            result = self.manager.upload_file(self.current_server, file_path)
            if result:
                print(f"✅ 上传成功！")
                print(f"🔗 访问地址: {result}")
                
                # 询问是否继续
                if input("\n是否继续上传其他文件? (y/N): ").lower() != 'y':
                    break
            else:
                print("❌ 上传失败")
    
    def show_menu(self):
        """显示操作菜单"""
        print(f"\n🎯 当前连接: {self.manager.servers[self.current_server]['name']}")
        print("-" * 40)
        print("1. 查看文件列表")
        print("2. 上传文件")
        print("3. 切换服务器")
        print("4. 退出程序")
        print("-" * 40)
    
    def run(self):
        """运行主程序"""
        self.show_banner()
        
        # 首次连接
        while True:
            self.show_servers()
            choice = input("请选择服务器 (1-2): ").strip()
            
            if self.connect_and_list(choice):
                break
            else:
                retry = input("是否重试? (y/N): ").lower()
                if retry != 'y':
                    print("👋 感谢使用！")
                    return
        
        # 主操作循环
        while True:
            try:
                self.show_menu()
                choice = input("请选择操作: ").strip()
                
                if choice == '1':
                    print("\n🔄 刷新文件列表...")
                    self.manager.list_files(self.current_server)
                    
                elif choice == '2':
                    self.upload_file_simple()
                    
                elif choice == '3':
                    # 断开当前连接
                    self.manager.disconnect_server(self.current_server)
                    self.current_server = None
                    
                    # 重新选择服务器
                    while True:
                        self.show_servers()
                        server_choice = input("请选择服务器 (1-2): ").strip()
                        if self.connect_and_list(server_choice):
                            break
                        else:
                            retry = input("是否重试? (y/N): ").lower()
                            if retry != 'y':
                                print("👋 感谢使用！")
                                return
                    
                elif choice == '4':
                    print("👋 感谢使用项老师SSH文件管理器！")
                    break
                    
                else:
                    print("❌ 无效选择，请重新输入")
                
                input("\n按回车键继续...")
                
            except KeyboardInterrupt:
                print("\n\n👋 用户取消操作")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {str(e)}")
                input("按回车键继续...")
        
        # 关闭连接
        if self.current_server:
            self.manager.disconnect_server(self.current_server)

if __name__ == "__main__":
    try:
        manager = SimpleSSHManager()
        manager.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        input("按回车键退出...")
    finally:
        # 确保清理连接
        try:
            if 'manager' in locals():
                manager.manager.close_all_connections()
        except:
            pass
