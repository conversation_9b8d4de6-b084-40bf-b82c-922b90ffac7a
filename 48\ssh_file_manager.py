#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH+SFTP文件管理器
功能完整的远程文件管理工具
"""

import paramiko
import os
import sys
import stat
import time
from datetime import datetime
from pathlib import Path

class SSHFileManager:
    def __init__(self):
        # 服务器配置信息
        self.servers = {
            'server1': {
                'name': '调研报告服务器',
                'host': '************',
                'port': 22,
                'username': 'root',
                'password': 'Zxczxczxc111.',
                'remote_path': '/www/wwwroot/www.diwangzhidao.com/',
                'url_base': 'https://www.diwangzhidao.com/'
            },
            'server2': {
                'name': '说明文档服务器',
                'host': '*************',
                'port': 22,
                'username': 'root',
                'password': 'Zxczxczxc111.',
                'remote_path': '/www/wwwroot/www.xli8.com/',
                'url_base': 'http://www.xli8.com/'
            }
        }
        self.current_connections = {}  # 保存活跃连接
    
    def connect_server(self, server_name):
        """连接到指定服务器"""
        if server_name not in self.servers:
            print(f"❌ 服务器配置不存在: {server_name}")
            return None, None
            
        server_config = self.servers[server_name]
        
        try:
            print(f"🔗 正在连接到 {server_config['name']} ({server_config['host']})...")
            
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            ssh.connect(
                hostname=server_config['host'],
                port=server_config['port'],
                username=server_config['username'],
                password=server_config['password'],
                timeout=30
            )
            
            sftp = ssh.open_sftp()
            
            # 保存连接
            self.current_connections[server_name] = {
                'ssh': ssh,
                'sftp': sftp,
                'config': server_config
            }
            
            print(f"✅ 连接成功！")
            return ssh, sftp
            
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
            return None, None
    
    def disconnect_server(self, server_name):
        """断开服务器连接"""
        if server_name in self.current_connections:
            try:
                self.current_connections[server_name]['sftp'].close()
                self.current_connections[server_name]['ssh'].close()
                del self.current_connections[server_name]
                print(f"🔌 已断开 {server_name} 连接")
            except:
                pass
    
    def format_file_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f}{unit}"
            size /= 1024.0
        return f"{size:.1f}TB"
    
    def format_permissions(self, mode):
        """格式化文件权限"""
        permissions = ""
        permissions += 'r' if mode & stat.S_IRUSR else '-'
        permissions += 'w' if mode & stat.S_IWUSR else '-'
        permissions += 'x' if mode & stat.S_IXUSR else '-'
        permissions += 'r' if mode & stat.S_IRGRP else '-'
        permissions += 'w' if mode & stat.S_IWGRP else '-'
        permissions += 'x' if mode & stat.S_IXGRP else '-'
        permissions += 'r' if mode & stat.S_IROTH else '-'
        permissions += 'w' if mode & stat.S_IWOTH else '-'
        permissions += 'x' if mode & stat.S_IXOTH else '-'
        return permissions
    
    def list_files(self, server_name, remote_path=None):
        """列出远程目录文件"""
        if server_name not in self.current_connections:
            ssh, sftp = self.connect_server(server_name)
            if not sftp:
                return []
        else:
            sftp = self.current_connections[server_name]['sftp']
            ssh = self.current_connections[server_name]['ssh']

        if remote_path is None:
            remote_path = self.servers[server_name]['remote_path']

        try:
            print(f"\n📁 {remote_path}")
            print("-" * 80)
            print(f"{'类型':<4} {'权限':<10} {'大小':<10} {'修改时间':<20} {'文件名'}")
            print("-" * 80)

            files = []

            # 使用SSH命令获取文件列表，避免编码问题
            stdin, stdout, stderr = ssh.exec_command(f'ls -la "{remote_path}"')
            output = stdout.read().decode('utf-8', errors='ignore')

            if output:
                lines = output.strip().split('\n')[1:]  # 跳过第一行总计信息

                for line in lines:
                    if not line.strip():
                        continue

                    parts = line.split()
                    if len(parts) < 9:
                        continue

                    permissions = parts[0]
                    size_str = parts[4]

                    # 文件名可能包含空格，所以从第8个部分开始拼接
                    filename = ' '.join(parts[8:])

                    # 跳过 . 和 ..
                    if filename in ['.', '..']:
                        continue

                    # 判断文件类型
                    file_type = "📁" if permissions.startswith('d') else "📄"
                    is_dir = permissions.startswith('d')

                    # 格式化大小
                    try:
                        size = int(size_str)
                        size_formatted = self.format_file_size(size)
                    except:
                        size = 0
                        size_formatted = "-"

                    # 修改时间 (简化显示)
                    mtime = ' '.join(parts[5:8])

                    print(f"{file_type:<4} {permissions:<10} {size_formatted:<10} {mtime:<20} {filename}")

                    files.append({
                        'name': filename,
                        'type': 'dir' if is_dir else 'file',
                        'size': size,
                        'permissions': permissions,
                        'mtime': mtime,
                        'full_path': remote_path.rstrip('/') + '/' + filename
                    })

            print("-" * 80)
            print(f"共 {len(files)} 个项目")
            return files

        except Exception as e:
            print(f"❌ 列出文件失败: {str(e)}")
            return []
    
    def upload_file(self, server_name, local_file, remote_path=None, remote_filename=None):
        """上传文件"""
        if server_name not in self.current_connections:
            ssh, sftp = self.connect_server(server_name)
            if not sftp:
                return None
        else:
            sftp = self.current_connections[server_name]['sftp']
            ssh = self.current_connections[server_name]['ssh']
            
        if not os.path.exists(local_file):
            print(f"❌ 本地文件不存在: {local_file}")
            return None
            
        if remote_path is None:
            remote_path = self.servers[server_name]['remote_path']
            
        if remote_filename is None:
            remote_filename = os.path.basename(local_file)
            
        remote_file_path = remote_path.rstrip('/') + '/' + remote_filename
        
        try:
            print(f"📤 上传: {local_file} -> {remote_file_path}")
            
            # 确保远程目录存在
            ssh.exec_command(f"mkdir -p {remote_path}")
            
            # 上传文件
            sftp.put(local_file, remote_file_path)
            sftp.chmod(remote_file_path, 0o644)
            
            # 生成访问URL
            relative_path = remote_file_path.replace(self.servers[server_name]['remote_path'], '')
            access_url = self.servers[server_name]['url_base'] + relative_path
            
            print(f"✅ 上传成功！")
            print(f"🔗 访问地址: {access_url}")
            
            return access_url
            
        except Exception as e:
            print(f"❌ 上传失败: {str(e)}")
            return None
    
    def download_file(self, server_name, remote_file, local_path=None):
        """下载文件"""
        if server_name not in self.current_connections:
            ssh, sftp = self.connect_server(server_name)
            if not sftp:
                return False
        else:
            sftp = self.current_connections[server_name]['sftp']
            
        if local_path is None:
            local_path = os.path.basename(remote_file)
            
        try:
            print(f"📥 下载: {remote_file} -> {local_path}")
            sftp.get(remote_file, local_path)
            print(f"✅ 下载成功！")
            return True
            
        except Exception as e:
            print(f"❌ 下载失败: {str(e)}")
            return False
    
    def delete_file(self, server_name, remote_file):
        """删除远程文件"""
        if server_name not in self.current_connections:
            ssh, sftp = self.connect_server(server_name)
            if not sftp:
                return False
        else:
            sftp = self.current_connections[server_name]['sftp']
            
        try:
            print(f"🗑️ 删除: {remote_file}")
            sftp.remove(remote_file)
            print(f"✅ 删除成功！")
            return True
            
        except Exception as e:
            print(f"❌ 删除失败: {str(e)}")
            return False
    
    def create_directory(self, server_name, remote_dir):
        """创建远程目录"""
        if server_name not in self.current_connections:
            ssh, sftp = self.connect_server(server_name)
            if not ssh:
                return False
        else:
            ssh = self.current_connections[server_name]['ssh']
            
        try:
            print(f"📁 创建目录: {remote_dir}")
            ssh.exec_command(f"mkdir -p {remote_dir}")
            print(f"✅ 目录创建成功！")
            return True
            
        except Exception as e:
            print(f"❌ 创建目录失败: {str(e)}")
            return False
    
    def get_file_info(self, server_name, remote_file):
        """获取文件详细信息"""
        if server_name not in self.current_connections:
            ssh, sftp = self.connect_server(server_name)
            if not sftp:
                return None
        else:
            sftp = self.current_connections[server_name]['sftp']
            
        try:
            stat_info = sftp.stat(remote_file)
            
            info = {
                'size': stat_info.st_size,
                'size_formatted': self.format_file_size(stat_info.st_size),
                'permissions': self.format_permissions(stat_info.st_mode),
                'mtime': datetime.fromtimestamp(stat_info.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                'is_dir': stat.S_ISDIR(stat_info.st_mode),
                'is_file': stat.S_ISREG(stat_info.st_mode)
            }
            
            return info
            
        except Exception as e:
            print(f"❌ 获取文件信息失败: {str(e)}")
            return None
    
    def close_all_connections(self):
        """关闭所有连接"""
        for server_name in list(self.current_connections.keys()):
            self.disconnect_server(server_name)

if __name__ == "__main__":
    manager = SSHFileManager()
    
    print("🎯 项老师SSH文件管理器")
    print("=" * 50)
    
    # 测试连接
    for server_name in manager.servers:
        print(f"\n测试 {manager.servers[server_name]['name']}:")
        manager.list_files(server_name)
    
    manager.close_all_connections()
    print("\n🎉 测试完成！")
