#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - 酷炫版FTP风格可执行程序构建
全新酷炫UI设计，去掉标题栏，青色科技风格
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def create_cyber_ftp_spec():
    """创建酷炫版FTP风格PyInstaller配置"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ssh_ftp_manager.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ssh_file_manager.py', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'paramiko',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.simpledialog',
        'threading',
        'subprocess',
        'datetime',
        'pathlib',
        'stat',
        'os',
        'sys',
        'shutil',
        'time'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'PIL'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='项老师SSH文件管理器_酷炫版_v1.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.ico' if os.path.exists('logo.ico') else None,
)
'''
    
    with open('ssh_cyber_ftp.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 酷炫版FTP风格配置文件创建完成")

def build_cyber_ftp_exe():
    """构建酷炫版FTP风格可执行文件"""
    print("🎯 项老师SSH文件管理器 - 酷炫版FTP风格构建")
    print("=" * 60)
    
    try:
        # 清理之前的构建
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # 创建配置文件
        create_cyber_ftp_spec()
        
        print("🔨 开始构建酷炫版FTP风格可执行文件...")
        
        # 构建
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'ssh_cyber_ftp.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 酷炫版FTP风格构建成功！")
            
            # 创建发布目录
            build_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            release_dir = f"项老师SSH文件管理器_酷炫版_{build_time}"
            
            if os.path.exists(release_dir):
                shutil.rmtree(release_dir)
            os.makedirs(release_dir)
            
            # 复制可执行文件
            exe_path = "dist/项老师SSH文件管理器_酷炫版_v1.0.exe"
            if os.path.exists(exe_path):
                shutil.copy2(exe_path, release_dir)
                print(f"✅ 酷炫版已复制到 {release_dir}")
            
            # 复制说明文档
            if os.path.exists('README.md'):
                shutil.copy2('README.md', release_dir)
            
            # 创建酷炫版功能说明
            cyber_guide = f'''# 🎯 项老师SSH文件管理器 - 酷炫版

## 🚀 快速开始
双击运行: 项老师SSH文件管理器_酷炫版_v1.0.exe

## ✨ 全新酷炫UI设计

### 🎨 科技感色系
✅ **深色主题** - #1f293a 深蓝灰背景，科技感十足
✅ **青色高亮** - #0ef 青色作为主要强调色
✅ **层次分明** - #2c4766 中间色调，营造层次感
✅ **去掉标题栏** - 更简洁的界面，节省空间

### 🌟 视觉升级
✅ **按钮样式** - 酷炫的Cyber风格按钮设计
✅ **输入框** - 科技感边框，聚焦时发光效果
✅ **文件列表** - 深色背景配青色标题
✅ **状态栏** - 发光的连接状态指示
✅ **呼吸动画** - 连接状态的呼吸灯效果

### 🎯 界面优化
✅ **紧凑布局** - 去掉标题栏后界面更紧凑
✅ **图标按钮** - 使用emoji图标，更直观
✅ **颜色协调** - 统一的青色科技风格
✅ **动画效果** - 微妙的呼吸灯动画

## 🌟 完整功能保留

### 🎨 文件图标与颜色系统
✅ **30+种文件图标** - 每种文件类型专属图标
✅ **8种颜色分类** - 科技感配色方案
- 📁 文件夹 (青色)、🖼️ 图片 (粉色)、🌐 网页 (青绿色)
- 📝 文档 (浅绿色)、📦 压缩包 (浅紫色)、🎵 音频 (金黄色)等

### 📊 智能列排序功能
✅ **点击列标题排序** - 所有列都支持智能排序
✅ **文件夹优先** - 智能排序算法
✅ **大小识别** - 自动识别B/KB/MB/GB单位

### 🖥️ 便捷操作功能
✅ **桌面快捷** - 🖥️ 按钮一键跳转桌面
✅ **三点导航** - 文件列表顶部"..."返回上级
✅ **图标按钮** - 🔄🎯🏠⬆️等直观图标

### 🔄 完整文件管理
✅ **双向传输** - 本地↔远程文件上传下载
✅ **文件操作** - 新建、删除、重命名
✅ **右键菜单** - 丰富的上下文操作
✅ **访问链接** - 一键生成文件URL

## 🎮 操作体验

### 🎨 视觉体验
- **科技感十足** - 深色背景配青色高亮
- **层次分明** - 不同区域使用不同深度的颜色
- **动画效果** - 连接状态的呼吸灯动画
- **简洁布局** - 去掉标题栏，界面更紧凑

### ⚡ 操作体验
- **图标直观** - 所有按钮都使用emoji图标
- **颜色识别** - 文件类型通过颜色快速识别
- **智能排序** - 点击列标题即可排序
- **快捷操作** - 桌面按钮、三点导航等

## 🔐 服务器配置

### 服务器1 - 调研报告服务器
- **地址**: 8.138.203.25
- **根目录**: /www/wwwroot/www.diwangzhidao.com/
- **网站**: https://www.diwangzhidao.com/

### 服务器2 - 说明文档服务器
- **地址**: 39.97.189.146
- **根目录**: /www/wwwroot/www.xli8.com/
- **网站**: http://www.xli8.com/

## 💡 设计理念

### 🎨 色彩搭配
- **主背景**: #1f293a (深蓝灰) - 沉稳科技感
- **次背景**: #2c4766 (中蓝灰) - 层次分明
- **强调色**: #0ef (青色) - 科技感高亮
- **输入框**: #212121 (深灰) - 专业输入体验

### 🌟 动画效果
- **呼吸灯**: 连接状态的微妙呼吸动画
- **聚焦效果**: 输入框聚焦时的发光边框
- **按钮反馈**: 鼠标悬停和点击的视觉反馈

### 🎯 用户体验
- **去掉冗余**: 移除占位置的标题栏
- **图标化**: 按钮使用直观的emoji图标
- **紧凑布局**: 最大化文件列表显示空间

## 🛡️ 技术特性
- **SSH+SFTP加密**: 所有数据传输完全加密
- **跨平台兼容**: Windows全版本支持
- **无依赖运行**: 独立可执行文件
- **稳定可靠**: 完善的错误处理机制

## 📦 系统要求
- **操作系统**: Windows 7/8/10/11
- **架构**: x64
- **依赖**: 无需安装任何环境
- **网络**: 需要能访问服务器的网络

## 🎉 版本亮点

这是项老师SSH文件管理器的酷炫UI升级版，具有以下突出特色：

✅ **全新酷炫UI** - 科技感十足的深色主题设计
✅ **去掉标题栏** - 更简洁紧凑的界面布局
✅ **青色科技风** - 统一的青色高亮配色方案
✅ **动画效果** - 微妙的呼吸灯和聚焦动画
✅ **图标按钮** - 直观的emoji图标设计
✅ **完整功能** - 保留所有文件管理功能

这是一个真正酷炫、专业、功能完整的SSH文件管理工具！

---
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
© 2025 项老师AI工作室 | 传统企业AI自动化转型专家
'''
            
            with open(f'{release_dir}/酷炫版功能说明.txt', 'w', encoding='utf-8') as f:
                f.write(cyber_guide)
            
            # 创建快速启动批处理
            batch_content = f'''@echo off
chcp 65001 >nul
title 项老师SSH文件管理器 - 酷炫版

echo.
echo 🎯 项老师SSH文件管理器 - 酷炫版
echo.
echo ✨ 全新酷炫UI特色:
echo    • 🎨 科技感深色主题
echo    • 💫 青色高亮配色
echo    • 🚀 去掉标题栏设计
echo    • 🌟 呼吸灯动画效果
echo    • 🎯 图标化按钮设计
echo.
echo 正在启动酷炫科技界面...
echo.

"项老师SSH文件管理器_酷炫版_v1.0.exe"

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败，请检查：
    echo 1. 是否被杀毒软件拦截
    echo 2. 是否有足够的系统权限
    echo 3. 系统是否支持图形界面
    echo.
    pause
)
'''
            
            with open(f'{release_dir}/启动SSH管理器酷炫版.bat', 'w', encoding='utf-8') as f:
                f.write(batch_content)
            
            print(f"🎉 酷炫版FTP风格构建完成: {release_dir}")
            
            # 显示文件大小
            exe_size = os.path.getsize(f"{release_dir}/项老师SSH文件管理器_酷炫版_v1.0.exe") / (1024*1024)
            print(f"📦 文件大小: {exe_size:.1f} MB")
            
            return True
            
        else:
            print(f"❌ 构建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = build_cyber_ftp_exe()
    
    if success:
        print("\n✅ 酷炫版SSH文件管理器构建成功！")
        print("💡 全新酷炫UI设计，去掉标题栏，青色科技风格")
        print("🚀 这是视觉效果最酷炫的版本")
        print("🎯 强烈推荐体验全新UI设计")
    else:
        print("\n❌ 酷炫版构建失败")
    
    input("\n按回车键退出...")
