#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - 主启动器
独立可执行版本
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
import threading

class SSHManagerLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("项老师SSH文件管理器 v1.0")
        self.root.geometry("600x400")
        self.root.configure(bg='#2b2b2b')
        self.root.resizable(False, False)
        
        # 设置图标
        try:
            # 如果有logo文件，设置为图标
            if os.path.exists('logo.ico'):
                self.root.iconbitmap('logo.ico')
        except:
            pass
        
        self.create_widgets()
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (400 // 2)
        self.root.geometry(f"600x400+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 标题区域
        title_frame = tk.Frame(self.root, bg='#2b2b2b', height=100)
        title_frame.pack(fill='x', padx=20, pady=20)
        title_frame.pack_propagate(False)
        
        # 主标题
        title_label = tk.Label(title_frame, 
                              text="🎯 项老师SSH文件管理器",
                              bg='#2b2b2b', fg='#ffffff',
                              font=('Microsoft YaHei', 20, 'bold'))
        title_label.pack(pady=10)
        
        # 副标题
        subtitle_label = tk.Label(title_frame,
                                 text="安全 • 高效 • 专业的文件管理解决方案",
                                 bg='#2b2b2b', fg='#cccccc',
                                 font=('Microsoft YaHei', 12))
        subtitle_label.pack()
        
        # 功能按钮区域
        button_frame = tk.Frame(self.root, bg='#2b2b2b')
        button_frame.pack(fill='both', expand=True, padx=40, pady=20)
        
        # 按钮样式
        button_style = {
            'font': ('Microsoft YaHei', 12, 'bold'),
            'width': 25,
            'height': 2,
            'relief': 'flat',
            'cursor': 'hand2'
        }
        
        # 图形界面版按钮
        gui_btn = tk.Button(button_frame,
                           text="🖥️ 图形界面版 (推荐)",
                           bg='#4CAF50', fg='white',
                           command=self.launch_gui,
                           **button_style)
        gui_btn.pack(pady=10, fill='x')
        
        # 命令行版按钮
        cli_btn = tk.Button(button_frame,
                           text="💻 命令行版 (专业)",
                           bg='#2196F3', fg='white',
                           command=self.launch_cli,
                           **button_style)
        cli_btn.pack(pady=10, fill='x')
        
        # 快速上传按钮
        upload_btn = tk.Button(button_frame,
                              text="📤 快速上传工具",
                              bg='#FF9800', fg='white',
                              command=self.launch_upload,
                              **button_style)
        upload_btn.pack(pady=10, fill='x')
        
        # 测试连接按钮
        test_btn = tk.Button(button_frame,
                            text="🔍 测试服务器连接",
                            bg='#9C27B0', fg='white',
                            command=self.test_connection,
                            **button_style)
        test_btn.pack(pady=10, fill='x')
        
        # 底部信息
        info_frame = tk.Frame(self.root, bg='#2b2b2b', height=60)
        info_frame.pack(fill='x', side='bottom')
        info_frame.pack_propagate(False)
        
        info_label = tk.Label(info_frame,
                             text="© 2025 项老师AI工作室 | 传统企业AI自动化转型专家",
                             bg='#2b2b2b', fg='#888888',
                             font=('Microsoft YaHei', 10))
        info_label.pack(pady=20)
    
    def launch_gui(self):
        """启动图形界面版"""
        try:
            from ssh_gui_manager import SSHGUIManager
            self.root.withdraw()  # 隐藏启动器
            app = SSHGUIManager()
            app.run()
            self.root.deiconify()  # 显示启动器
        except Exception as e:
            messagebox.showerror("启动失败", f"图形界面版启动失败:\n{str(e)}")
    
    def launch_cli(self):
        """启动命令行版"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, "ssh_cli_manager.py"], 
                           creationflags=subprocess.CREATE_NEW_CONSOLE)
        except Exception as e:
            messagebox.showerror("启动失败", f"命令行版启动失败:\n{str(e)}")
    
    def launch_upload(self):
        """启动快速上传"""
        try:
            from quick_upload import quick_upload
            threading.Thread(target=quick_upload, daemon=True).start()
        except Exception as e:
            messagebox.showerror("启动失败", f"快速上传工具启动失败:\n{str(e)}")
    
    def test_connection(self):
        """测试连接"""
        def test_thread():
            try:
                from ssh_file_manager import SSHFileManager
                manager = SSHFileManager()
                
                results = []
                for server_name, config in manager.servers.items():
                    ssh, sftp = manager.connect_server(server_name)
                    if ssh and sftp:
                        results.append(f"✅ {config['name']}: 连接成功")
                        manager.disconnect_server(server_name)
                    else:
                        results.append(f"❌ {config['name']}: 连接失败")
                
                result_text = "\n".join(results)
                messagebox.showinfo("连接测试结果", result_text)
                
            except Exception as e:
                messagebox.showerror("测试失败", f"连接测试失败:\n{str(e)}")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def run(self):
        """运行启动器"""
        self.root.mainloop()

if __name__ == "__main__":
    # 导入所有必要的模块
    try:
        from ssh_file_manager import SSHFileManager
        from ssh_gui_manager import SSHGUIManager
        import paramiko
    except ImportError as e:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        tk.messagebox.showerror("依赖缺失", f"缺少必要的依赖包:\n{str(e)}\n\n请安装: pip install paramiko")
        sys.exit(1)
    
    launcher = SSHManagerLauncher()
    launcher.run()
