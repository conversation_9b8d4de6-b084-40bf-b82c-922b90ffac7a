#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - 简化版可执行程序构建
专门构建命令行版本，避免GUI问题
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def create_simple_spec():
    """创建简化版PyInstaller配置"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ssh_simple_manager.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ssh_file_manager.py', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'paramiko',
        'threading',
        'subprocess',
        'datetime',
        'pathlib',
        'stat',
        'os',
        'sys'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'PIL'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='项老师SSH文件管理器_简化版_v1.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('ssh_simple.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 简化版配置文件创建完成")

def build_simple_exe():
    """构建简化版可执行文件"""
    print("🎯 项老师SSH文件管理器 - 简化版构建")
    print("=" * 50)
    
    try:
        # 清理之前的构建
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # 创建配置文件
        create_simple_spec()
        
        print("🔨 开始构建简化版可执行文件...")
        
        # 构建
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'ssh_simple.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 简化版构建成功！")
            
            # 创建发布目录
            build_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            release_dir = f"项老师SSH文件管理器_简化版_{build_time}"
            
            if os.path.exists(release_dir):
                shutil.rmtree(release_dir)
            os.makedirs(release_dir)
            
            # 复制可执行文件
            exe_path = "dist/项老师SSH文件管理器_简化版_v1.0.exe"
            if os.path.exists(exe_path):
                shutil.copy2(exe_path, release_dir)
                print(f"✅ 简化版已复制到 {release_dir}")
            
            # 复制说明文档
            if os.path.exists('README.md'):
                shutil.copy2('README.md', release_dir)
            
            # 创建使用说明
            usage_guide = f'''# 🎯 项老师SSH文件管理器 - 简化版

## 🚀 快速开始
双击运行: 项老师SSH文件管理器_简化版_v1.0.exe

## 📋 功能特点
✅ 命令行界面，稳定可靠
✅ 双服务器支持
✅ 文件查看和上传功能
✅ 自动生成访问链接
✅ 无需安装Python环境

## 🖥️ 使用方法
1. 双击运行exe文件
2. 选择要连接的服务器 (1或2)
3. 查看文件列表或上传文件
4. 按提示操作即可

## 🔐 服务器信息
- 服务器1: 调研报告服务器 (8.138.203.25)
- 服务器2: 说明文档服务器 (39.97.189.146)

## 💡 操作提示
- 上传文件时可以拖拽文件到命令行窗口
- 支持中文文件名
- 上传成功后会自动生成访问链接
- 输入 'q' 可以退出上传模式

## 🛡️ 安全特性
- SSH+SFTP加密传输
- 自动连接管理
- 完善的错误处理

---
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
© 2025 项老师AI工作室
'''
            
            with open(f'{release_dir}/使用说明.txt', 'w', encoding='utf-8') as f:
                f.write(usage_guide)
            
            # 创建快速启动批处理
            batch_content = f'''@echo off
chcp 65001 >nul
title 项老师SSH文件管理器

echo.
echo 🎯 项老师SSH文件管理器 - 简化版
echo.
echo 正在启动...
echo.

"项老师SSH文件管理器_简化版_v1.0.exe"

pause
'''
            
            with open(f'{release_dir}/启动SSH管理器.bat', 'w', encoding='utf-8') as f:
                f.write(batch_content)
            
            print(f"🎉 简化版构建完成: {release_dir}")
            
            # 显示文件大小
            exe_size = os.path.getsize(f"{release_dir}/项老师SSH文件管理器_简化版_v1.0.exe") / (1024*1024)
            print(f"📦 文件大小: {exe_size:.1f} MB")
            
            return True
            
        else:
            print(f"❌ 构建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = build_simple_exe()
    
    if success:
        print("\n✅ 简化版独立可执行程序构建成功！")
        print("💡 这是纯命令行版本，避免了GUI相关问题")
        print("🚀 可以直接双击运行，无需Python环境")
    else:
        print("\n❌ 简化版构建失败")
    
    input("\n按回车键退出...")
