#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - 最终版FTP风格可执行程序构建
修改为网站根目录，使用"..."进入上级目录
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def create_final_ftp_spec():
    """创建最终版FTP风格PyInstaller配置"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ssh_ftp_manager.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ssh_file_manager.py', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'paramiko',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.simpledialog',
        'threading',
        'subprocess',
        'datetime',
        'pathlib',
        'stat',
        'os',
        'sys',
        'shutil'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'PIL'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='项老师SSH文件管理器_最终版_v1.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.ico' if os.path.exists('logo.ico') else None,
)
'''
    
    with open('ssh_final_ftp.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 最终版FTP风格配置文件创建完成")

def build_final_ftp_exe():
    """构建最终版FTP风格可执行文件"""
    print("🎯 项老师SSH文件管理器 - 最终版FTP风格构建")
    print("=" * 60)
    
    try:
        # 清理之前的构建
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # 创建配置文件
        create_final_ftp_spec()
        
        print("🔨 开始构建最终版FTP风格可执行文件...")
        
        # 构建
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'ssh_final_ftp.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 最终版FTP风格构建成功！")
            
            # 创建发布目录
            build_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            release_dir = f"项老师SSH文件管理器_最终版_{build_time}"
            
            if os.path.exists(release_dir):
                shutil.rmtree(release_dir)
            os.makedirs(release_dir)
            
            # 复制可执行文件
            exe_path = "dist/项老师SSH文件管理器_最终版_v1.0.exe"
            if os.path.exists(exe_path):
                shutil.copy2(exe_path, release_dir)
                print(f"✅ 最终版已复制到 {release_dir}")
            
            # 复制说明文档
            if os.path.exists('README.md'):
                shutil.copy2('README.md', release_dir)
            
            # 创建最终版功能说明
            final_guide = f'''# 🎯 项老师SSH文件管理器 - 最终版

## 🚀 快速开始
双击运行: 项老师SSH文件管理器_最终版_v1.0.exe

## ✨ 最新改进
✅ **网站根目录访问** - 直接从网站根目录开始浏览
✅ **三点导航** - 文件列表顶部显示"..."点击进入上级目录
✅ **完整目录权限** - 可在整个网站目录范围内自由导航
✅ **专业FTP体验** - 完全模拟专业FTP客户端操作

## 🌟 核心功能

### 🖥️ 双面板设计
- **左侧面板**: 本地文件浏览器
- **右侧面板**: 远程文件浏览器（从网站根目录开始）
- **同步操作**: 左右面板独立操作，实时同步

### 📁 智能目录导航
✅ **三点上级** - 文件列表最顶部的"..."点击进入上级目录
✅ **双击进入** - 双击文件夹直接进入
✅ **根目录按钮** - 快速返回网站根目录
✅ **地址栏跳转** - 直接输入路径快速跳转
✅ **路径显示** - 实时显示当前完整路径

### 🔄 完整文件操作
✅ **双向传输** - 本地↔远程文件上传下载
✅ **文件管理** - 新建、删除、重命名文件夹
✅ **右键菜单** - 丰富的上下文操作菜单
✅ **访问链接** - 一键生成文件直接访问URL
✅ **批量操作** - 支持多文件选择和操作

## 🎯 操作指南

### 连接服务器
1. 点击顶部"调研报告服务器"或"说明文档服务器"
2. 连接成功后右侧显示网站根目录文件
3. 左侧显示本地文件，可自由浏览

### 目录导航
- **进入文件夹**: 双击文件夹名称
- **返回上级**: 点击文件列表最顶部的"..."
- **快速跳转**: 在地址栏输入完整路径按回车
- **返回根目录**: 点击"🏠 根目录"按钮

### 文件传输
- **上传**: 选中本地文件，点击"📤 上传"或右键菜单
- **下载**: 选中远程文件，点击"📥 下载"或右键菜单
- **批量操作**: 使用Ctrl+点击选择多个文件

### 文件管理
- **新建文件夹**: 点击对应面板的"📁 新建文件夹"
- **删除文件**: 选中文件后点击"🗑️ 删除"或右键菜单
- **重命名**: 右键本地文件选择"📝 重命名"
- **复制链接**: 右键远程文件选择"🌐 复制访问链接"

## 🔐 服务器配置

### 服务器1 - 调研报告服务器
- **地址**: 8.138.203.25
- **根目录**: /www/wwwroot/www.diwangzhidao.com/
- **网站**: https://www.diwangzhidao.com/

### 服务器2 - 说明文档服务器
- **地址**: 39.97.189.146
- **根目录**: /www/wwwroot/www.xli8.com/
- **网站**: http://www.xli8.com/

## 💡 高级技巧

### 快速导航
- **三点返回**: 文件列表最顶部的"..."是返回上级的快捷方式
- **地址栏**: 可以直接输入任何有权限的路径快速跳转
- **根目录**: 点击"🏠 根目录"快速回到网站根目录

### 文件操作
- **右键菜单**: 右键任何文件获取完整操作菜单
- **访问链接**: 远程文件右键可直接复制网页访问链接
- **路径复制**: 快速复制文件的完整服务器路径

### 效率提升
- **双面板**: 同时管理本地和远程文件，提高效率
- **实时状态**: 底部状态栏显示当前操作和连接状态
- **错误处理**: 自动处理网络问题和权限错误

## 🛡️ 安全特性
- **SSH+SFTP加密**: 所有数据传输完全加密
- **权限控制**: 自动设置合适的文件权限
- **安全范围**: 限制在网站目录范围内，防止误操作
- **操作确认**: 删除等危险操作需要确认

## 📦 系统要求
- **操作系统**: Windows 7/8/10/11
- **架构**: x64
- **依赖**: 无需安装Python或其他环境
- **网络**: 需要能访问服务器的网络环境

## 🎉 版本特色

这是项老师SSH文件管理器的最终完善版本，具有以下特色：

✅ **专业FTP体验** - 完全模拟专业FTP客户端操作
✅ **网站根目录** - 直接从网站根目录开始，方便网站管理
✅ **三点导航** - 直观的"..."上级目录导航
✅ **完整权限** - 在安全范围内的完整目录访问权限
✅ **双向管理** - 本地和远程文件的完整双向管理

这是一个真正专业级的SSH文件管理工具，完全可以替代传统FTP客户端！

---
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
© 2025 项老师AI工作室 | 传统企业AI自动化转型专家
'''
            
            with open(f'{release_dir}/最终版功能说明.txt', 'w', encoding='utf-8') as f:
                f.write(final_guide)
            
            # 创建快速启动批处理
            batch_content = f'''@echo off
chcp 65001 >nul
title 项老师SSH文件管理器 - 最终版

echo.
echo 🎯 项老师SSH文件管理器 - 最终版
echo.
echo ✨ 特色功能:
echo    • 网站根目录访问
echo    • 三点上级目录导航
echo    • 专业FTP客户端体验
echo.
echo 正在启动...
echo.

"项老师SSH文件管理器_最终版_v1.0.exe"

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败，请检查：
    echo 1. 是否被杀毒软件拦截
    echo 2. 是否有足够的系统权限
    echo 3. 系统是否支持图形界面
    echo.
    pause
)
'''
            
            with open(f'{release_dir}/启动SSH管理器最终版.bat', 'w', encoding='utf-8') as f:
                f.write(batch_content)
            
            print(f"🎉 最终版FTP风格构建完成: {release_dir}")
            
            # 显示文件大小
            exe_size = os.path.getsize(f"{release_dir}/项老师SSH文件管理器_最终版_v1.0.exe") / (1024*1024)
            print(f"📦 文件大小: {exe_size:.1f} MB")
            
            return True
            
        else:
            print(f"❌ 构建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = build_final_ftp_exe()
    
    if success:
        print("\n✅ 最终版SSH文件管理器构建成功！")
        print("💡 已修改为网站根目录访问，使用'...'进入上级目录")
        print("🚀 这是功能最完整、最符合要求的版本")
        print("🎯 强烈推荐作为主要使用版本")
    else:
        print("\n❌ 最终版构建失败")
    
    input("\n按回车键退出...")
