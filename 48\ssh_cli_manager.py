#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - 命令行交互版
功能完整的命令行文件管理工具
"""

import os
import sys
from ssh_file_manager import SSHFileManager

class SSHCLIManager:
    def __init__(self):
        self.manager = SSHFileManager()
        self.current_server = None
        self.current_path = None
        
    def show_banner(self):
        """显示程序横幅"""
        print("=" * 60)
        print("🎯 项老师SSH文件管理器 - 命令行版 v1.0")
        print("=" * 60)
        print("功能: 文件查看、上传、下载、删除、目录管理")
        print("支持: 2个服务器同时管理")
        print("=" * 60)
    
    def show_servers(self):
        """显示服务器列表"""
        print("\n📡 可用服务器:")
        print("-" * 40)
        for i, (server_name, config) in enumerate(self.manager.servers.items(), 1):
            status = "🟢 已连接" if server_name in self.manager.current_connections else "🔴 未连接"
            print(f"{i}. {config['name']}")
            print(f"   地址: {config['host']}")
            print(f"   状态: {status}")
            print()
    
    def show_menu(self):
        """显示主菜单"""
        print("\n🎯 主菜单:")
        print("-" * 30)
        print("1. 连接服务器")
        print("2. 查看文件列表")
        print("3. 上传文件")
        print("4. 下载文件")
        print("5. 删除文件")
        print("6. 创建目录")
        print("7. 切换目录")
        print("8. 文件信息")
        print("9. 断开连接")
        print("0. 退出程序")
        print("-" * 30)
    
    def connect_server(self):
        """连接服务器"""
        self.show_servers()
        
        try:
            choice = input("请选择服务器 (1-2): ").strip()
            server_names = list(self.manager.servers.keys())
            
            if choice == '1':
                server_name = server_names[0]
            elif choice == '2':
                server_name = server_names[1]
            else:
                print("❌ 无效选择")
                return
                
            ssh, sftp = self.manager.connect_server(server_name)
            if ssh and sftp:
                self.current_server = server_name
                self.current_path = self.manager.servers[server_name]['remote_path']
                print(f"✅ 已连接到 {self.manager.servers[server_name]['name']}")
                print(f"📁 当前目录: {self.current_path}")
            else:
                print("❌ 连接失败")
                
        except (ValueError, IndexError):
            print("❌ 无效输入")
    
    def list_files(self):
        """列出文件"""
        if not self.current_server:
            print("❌ 请先连接服务器")
            return
            
        print(f"\n📁 当前服务器: {self.manager.servers[self.current_server]['name']}")
        files = self.manager.list_files(self.current_server, self.current_path)
        
        if files:
            print(f"\n💡 提示: 输入文件编号可快速选择文件")
            for i, file_info in enumerate(files, 1):
                file_type = "📁" if file_info['type'] == 'dir' else "📄"
                print(f"{i:2d}. {file_type} {file_info['name']}")
    
    def upload_file(self):
        """上传文件"""
        if not self.current_server:
            print("❌ 请先连接服务器")
            return
            
        local_file = input("请输入本地文件路径 (支持拖拽): ").strip().strip('"')
        if not local_file:
            print("❌ 未指定文件")
            return
            
        if not os.path.exists(local_file):
            print(f"❌ 文件不存在: {local_file}")
            return
            
        # 询问是否自定义文件名
        custom_name = input("自定义文件名 (回车使用原名): ").strip()
        remote_filename = custom_name if custom_name else None
        
        result = self.manager.upload_file(self.current_server, local_file, 
                                        self.current_path, remote_filename)
        if result:
            print(f"🌐 访问地址: {result}")
            
            # 询问是否打开浏览器
            if input("是否在浏览器中打开? (y/N): ").lower() == 'y':
                try:
                    import webbrowser
                    webbrowser.open(result)
                    print("🌐 已在浏览器中打开")
                except:
                    print("❌ 无法打开浏览器")
    
    def download_file(self):
        """下载文件"""
        if not self.current_server:
            print("❌ 请先连接服务器")
            return
            
        files = self.manager.list_files(self.current_server, self.current_path)
        if not files:
            return
            
        # 显示文件列表供选择
        print("\n选择要下载的文件:")
        file_files = [f for f in files if f['type'] == 'file']
        
        if not file_files:
            print("❌ 当前目录没有文件")
            return
            
        for i, file_info in enumerate(file_files, 1):
            size = self.manager.format_file_size(file_info['size'])
            print(f"{i}. 📄 {file_info['name']} ({size})")
        
        try:
            choice = int(input("请选择文件编号: ")) - 1
            if 0 <= choice < len(file_files):
                selected_file = file_files[choice]
                remote_file = selected_file['full_path']
                
                # 询问保存位置
                local_path = input(f"保存为 (回车使用原名 '{selected_file['name']}'): ").strip()
                if not local_path:
                    local_path = selected_file['name']
                
                self.manager.download_file(self.current_server, remote_file, local_path)
            else:
                print("❌ 无效选择")
                
        except ValueError:
            print("❌ 请输入数字")
    
    def delete_file(self):
        """删除文件"""
        if not self.current_server:
            print("❌ 请先连接服务器")
            return
            
        files = self.manager.list_files(self.current_server, self.current_path)
        if not files:
            return
            
        print("\n选择要删除的文件:")
        for i, file_info in enumerate(files, 1):
            file_type = "📁" if file_info['type'] == 'dir' else "📄"
            print(f"{i}. {file_type} {file_info['name']}")
        
        try:
            choice = int(input("请选择文件编号: ")) - 1
            if 0 <= choice < len(files):
                selected_file = files[choice]
                
                # 确认删除
                confirm = input(f"确定删除 '{selected_file['name']}'? (y/N): ").lower()
                if confirm == 'y':
                    remote_file = selected_file['full_path']
                    self.manager.delete_file(self.current_server, remote_file)
                else:
                    print("❌ 取消删除")
            else:
                print("❌ 无效选择")
                
        except ValueError:
            print("❌ 请输入数字")
    
    def create_directory(self):
        """创建目录"""
        if not self.current_server:
            print("❌ 请先连接服务器")
            return
            
        dir_name = input("请输入目录名称: ").strip()
        if not dir_name:
            print("❌ 目录名不能为空")
            return
            
        remote_dir = self.current_path.rstrip('/') + '/' + dir_name
        self.manager.create_directory(self.current_server, remote_dir)
    
    def change_directory(self):
        """切换目录"""
        if not self.current_server:
            print("❌ 请先连接服务器")
            return
            
        files = self.manager.list_files(self.current_server, self.current_path)
        if not files:
            return
            
        # 显示目录列表
        dirs = [f for f in files if f['type'] == 'dir']
        
        print("\n可用目录:")
        print("0. .. (上级目录)")
        for i, dir_info in enumerate(dirs, 1):
            print(f"{i}. 📁 {dir_info['name']}")
        
        try:
            choice = int(input("请选择目录编号: "))
            
            if choice == 0:
                # 返回上级目录
                if self.current_path != self.manager.servers[self.current_server]['remote_path']:
                    self.current_path = '/'.join(self.current_path.rstrip('/').split('/')[:-1])
                    if not self.current_path:
                        self.current_path = '/'
                    print(f"📁 当前目录: {self.current_path}")
                else:
                    print("❌ 已在根目录")
            elif 1 <= choice <= len(dirs):
                selected_dir = dirs[choice - 1]
                self.current_path = selected_dir['full_path']
                print(f"📁 当前目录: {self.current_path}")
            else:
                print("❌ 无效选择")
                
        except ValueError:
            print("❌ 请输入数字")
    
    def show_file_info(self):
        """显示文件信息"""
        if not self.current_server:
            print("❌ 请先连接服务器")
            return
            
        files = self.manager.list_files(self.current_server, self.current_path)
        if not files:
            return
            
        print("\n选择要查看的文件:")
        for i, file_info in enumerate(files, 1):
            file_type = "📁" if file_info['type'] == 'dir' else "📄"
            print(f"{i}. {file_type} {file_info['name']}")
        
        try:
            choice = int(input("请选择文件编号: ")) - 1
            if 0 <= choice < len(files):
                selected_file = files[choice]
                remote_file = selected_file['full_path']
                
                info = self.manager.get_file_info(self.current_server, remote_file)
                if info:
                    print(f"\n📄 文件信息: {selected_file['name']}")
                    print("-" * 40)
                    print(f"类型: {'目录' if info['is_dir'] else '文件'}")
                    print(f"大小: {info['size_formatted']}")
                    print(f"权限: {info['permissions']}")
                    print(f"修改时间: {info['mtime']}")
                    
                    if not info['is_dir']:
                        # 生成访问URL
                        relative_path = remote_file.replace(
                            self.manager.servers[self.current_server]['remote_path'], '')
                        access_url = self.manager.servers[self.current_server]['url_base'] + relative_path
                        print(f"访问地址: {access_url}")
            else:
                print("❌ 无效选择")
                
        except ValueError:
            print("❌ 请输入数字")
    
    def disconnect_server(self):
        """断开服务器连接"""
        if self.current_server:
            self.manager.disconnect_server(self.current_server)
            print(f"🔌 已断开 {self.manager.servers[self.current_server]['name']} 连接")
            self.current_server = None
            self.current_path = None
        else:
            print("❌ 当前没有连接")
    
    def run(self):
        """运行程序主循环"""
        self.show_banner()
        
        while True:
            try:
                self.show_menu()
                
                if self.current_server:
                    server_name = self.manager.servers[self.current_server]['name']
                    print(f"当前连接: {server_name} | 路径: {self.current_path}")
                
                choice = input("\n请选择操作: ").strip()
                
                if choice == '1':
                    self.connect_server()
                elif choice == '2':
                    self.list_files()
                elif choice == '3':
                    self.upload_file()
                elif choice == '4':
                    self.download_file()
                elif choice == '5':
                    self.delete_file()
                elif choice == '6':
                    self.create_directory()
                elif choice == '7':
                    self.change_directory()
                elif choice == '8':
                    self.show_file_info()
                elif choice == '9':
                    self.disconnect_server()
                elif choice == '0':
                    print("👋 感谢使用项老师SSH文件管理器！")
                    break
                else:
                    print("❌ 无效选择，请重新输入")
                
                input("\n按回车键继续...")
                
            except KeyboardInterrupt:
                print("\n\n👋 用户取消操作")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {str(e)}")
                input("按回车键继续...")
        
        # 关闭所有连接
        self.manager.close_all_connections()

if __name__ == "__main__":
    cli = SSHCLIManager()
    cli.run()
