@echo off
chcp 65001 >nul
title 项老师SSH文件管理器启动器

:menu
cls
echo.
echo ========================================
echo 🎯 项老师SSH文件管理器启动器
echo ========================================
echo.
echo 请选择要启动的版本:
echo.
echo 1. 图形界面版 (推荐) - 可视化操作
echo 2. 命令行版 - 专业用户
echo 3. 测试连接 - 检查服务器状态
echo 4. 快速上传 - 拖拽上传文件
echo 0. 退出
echo.
echo ========================================

set /p choice=请输入选择 (0-4): 

if "%choice%"=="1" goto gui
if "%choice%"=="2" goto cli
if "%choice%"=="3" goto test
if "%choice%"=="4" goto upload
if "%choice%"=="0" goto exit
goto invalid

:gui
echo.
echo 🚀 启动图形界面版...
python ssh_gui_manager.py
goto menu

:cli
echo.
echo 🚀 启动命令行版...
python ssh_cli_manager.py
goto menu

:test
echo.
echo 🔍 测试服务器连接...
python ssh_file_manager.py
pause
goto menu

:upload
echo.
echo 📤 启动快速上传工具...
python quick_upload.py
goto menu

:invalid
echo.
echo ❌ 无效选择，请重新输入
timeout /t 2 >nul
goto menu

:exit
echo.
echo 👋 感谢使用项老师SSH文件管理器！
timeout /t 2 >nul
exit
