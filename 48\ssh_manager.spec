# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ssh_manager_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ssh_file_manager.py', '.'),
        ('ssh_gui_manager.py', '.'),
        ('ssh_cli_manager.py', '.'),
        ('quick_upload.py', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'paramiko',
        'tkinter',
        'threading',
        'subprocess',
        'datetime',
        'pathlib',
        'stat',
        'os',
        'sys'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='项老师SSH文件管理器_v1.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.ico' if os.path.exists('logo.ico') else None,
    version_file='version_info.txt' if os.path.exists('version_info.txt') else None,
)
