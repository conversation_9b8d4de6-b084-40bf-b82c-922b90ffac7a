#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项老师SSH文件管理器 - FTP风格完整版可执行程序构建
构建功能完整的类FTP客户端
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def create_ftp_spec():
    """创建FTP风格版PyInstaller配置"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ssh_ftp_manager.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ssh_file_manager.py', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'paramiko',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.simpledialog',
        'threading',
        'subprocess',
        'datetime',
        'pathlib',
        'stat',
        'os',
        'sys',
        'shutil'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'PIL'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='项老师SSH文件管理器_FTP风格_v1.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.ico' if os.path.exists('logo.ico') else None,
)
'''
    
    with open('ssh_ftp.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ FTP风格版配置文件创建完成")

def build_ftp_exe():
    """构建FTP风格版可执行文件"""
    print("🎯 项老师SSH文件管理器 - FTP风格完整版构建")
    print("=" * 60)
    
    try:
        # 清理之前的构建
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # 创建配置文件
        create_ftp_spec()
        
        print("🔨 开始构建FTP风格完整版可执行文件...")
        
        # 构建
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'ssh_ftp.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ FTP风格版构建成功！")
            
            # 创建发布目录
            build_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            release_dir = f"项老师SSH文件管理器_FTP风格完整版_{build_time}"
            
            if os.path.exists(release_dir):
                shutil.rmtree(release_dir)
            os.makedirs(release_dir)
            
            # 复制可执行文件
            exe_path = "dist/项老师SSH文件管理器_FTP风格_v1.0.exe"
            if os.path.exists(exe_path):
                shutil.copy2(exe_path, release_dir)
                print(f"✅ FTP风格版已复制到 {release_dir}")
            
            # 复制说明文档
            if os.path.exists('README.md'):
                shutil.copy2('README.md', release_dir)
            
            # 创建功能说明
            feature_guide = f'''# 🎯 项老师SSH文件管理器 - FTP风格完整版

## 🚀 快速开始
双击运行: 项老师SSH文件管理器_FTP风格_v1.0.exe

## 🌟 功能特点
✅ **双面板设计** - 左侧本地文件，右侧远程文件
✅ **完整目录导航** - 支持进入任意文件夹，返回上级目录
✅ **双向文件传输** - 本地↔远程文件上传下载
✅ **右键菜单** - 丰富的右键操作菜单
✅ **文件管理** - 重命名、删除、新建文件夹
✅ **路径导航** - 地址栏直接输入路径跳转
✅ **访问链接** - 自动生成远程文件访问URL

## 🖥️ 界面布局

### 左侧面板 - 本地文件
- 📁 本地文件浏览器
- 🔄 刷新按钮
- 📁 新建文件夹
- 📤 上传选中文件
- 📍 路径导航栏

### 右侧面板 - 远程文件  
- 🌐 远程文件浏览器
- 🔄 刷新按钮
- 📁 新建文件夹
- 📥 下载选中文件
- 🗑️ 删除选中文件
- 🏠 返回根目录
- ⬆️ 返回上级目录

## 🎯 操作指南

### 连接服务器
1. 点击顶部服务器按钮连接
2. 连接成功后右侧显示远程文件
3. 可随时点击"断开连接"

### 文件导航
- **双击文件夹**: 进入该文件夹
- **双击".."**: 返回上级目录
- **地址栏**: 直接输入路径按回车跳转
- **根目录按钮**: 快速返回服务器根目录

### 文件操作
- **上传**: 选中本地文件，点击"上传"或右键菜单
- **下载**: 选中远程文件，点击"下载"或右键菜单
- **删除**: 右键选择删除（支持本地和远程）
- **重命名**: 右键选择重命名（本地文件）
- **新建文件夹**: 点击对应面板的"新建文件夹"按钮

### 右键菜单功能
**本地文件右键**:
- 📤 上传到服务器
- 📝 重命名
- 🗑️ 删除
- 📋 复制路径

**远程文件右键**:
- 📥 下载到本地
- 🌐 复制访问链接
- 🗑️ 删除
- 📋 复制路径

## 🔐 服务器信息
- **服务器1**: 调研报告服务器 (8.138.203.25)
- **服务器2**: 说明文档服务器 (39.97.189.146)

## 💡 使用技巧
1. **批量操作**: 选中文件后使用按钮或右键菜单
2. **路径跳转**: 在地址栏输入完整路径快速跳转
3. **访问链接**: 右键远程文件可复制直接访问链接
4. **双面板**: 左右面板独立操作，方便文件管理

## 🛡️ 安全特性
- SSH+SFTP加密传输
- 自动连接管理
- 完善的错误处理
- 操作确认机制

## 📦 系统要求
- Windows 7/8/10/11
- 无需安装Python环境
- 支持中文文件名

---
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
© 2025 项老师AI工作室 | 传统企业AI自动化转型专家
'''
            
            with open(f'{release_dir}/FTP风格功能说明.txt', 'w', encoding='utf-8') as f:
                f.write(feature_guide)
            
            # 创建快速启动批处理
            batch_content = f'''@echo off
chcp 65001 >nul
title 项老师SSH文件管理器 - FTP风格

echo.
echo 🎯 项老师SSH文件管理器 - FTP风格完整版
echo.
echo 正在启动图形界面...
echo.

"项老师SSH文件管理器_FTP风格_v1.0.exe"

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败，请检查：
    echo 1. 是否被杀毒软件拦截
    echo 2. 是否有足够的系统权限
    echo 3. 系统是否支持图形界面
    echo.
    pause
)
'''
            
            with open(f'{release_dir}/启动FTP风格管理器.bat', 'w', encoding='utf-8') as f:
                f.write(batch_content)
            
            print(f"🎉 FTP风格版构建完成: {release_dir}")
            
            # 显示文件大小
            exe_size = os.path.getsize(f"{release_dir}/项老师SSH文件管理器_FTP风格_v1.0.exe") / (1024*1024)
            print(f"📦 文件大小: {exe_size:.1f} MB")
            
            return True
            
        else:
            print(f"❌ 构建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    # 先关闭可能运行的测试程序
    try:
        import psutil
        for proc in psutil.process_iter(['pid', 'name']):
            if 'ssh_ftp_manager' in proc.info['name']:
                proc.terminate()
    except:
        pass
    
    success = build_ftp_exe()
    
    if success:
        print("\n✅ FTP风格完整版独立可执行程序构建成功！")
        print("💡 这是功能最完整的版本，类似专业FTP客户端")
        print("🚀 支持完整的文件管理操作，双面板设计")
        print("🎯 推荐作为主要使用版本")
    else:
        print("\n❌ FTP风格版构建失败")
    
    input("\n按回车键退出...")
