@echo off
chcp 65001 >nul
title 项老师SSH上传工具

echo.
echo ========================================
echo 🎯 项老师SSH上传工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查paramiko是否安装
python -c "import paramiko" >nul 2>&1
if errorlevel 1 (
    echo 📦 正在安装依赖包...
    pip install paramiko
)

REM 运行上传工具
python quick_upload.py

pause
